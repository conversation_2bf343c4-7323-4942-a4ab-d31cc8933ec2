#!/usr/bin/env python3
"""
参数预设功能演示
展示所有策略标签页的参数预设功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys

def demo_grid_trading_presets():
    """演示网格交易参数预设"""
    print("🔍 演示网格交易参数预设功能")
    
    try:
        from strategy_tabs import GridTradingTab
        
        root = tk.Tk()
        root.title("网格交易参数预设演示")
        root.geometry("800x600")
        
        class MockMainApp:
            def __init__(self):
                self.strategies = {}
                self.strategy_threads = {}
        
        mock_app = MockMainApp()
        grid_tab = GridTradingTab(root, mock_app)
        grid_tab.frame.pack(fill=tk.BOTH, expand=True)
        
        # 显示预设配置
        presets = grid_tab.get_parameter_presets()
        print("✅ 网格交易预设配置:")
        for mode, config in presets.items():
            print(f"   {mode}: {config}")
        
        # 添加说明标签
        info_label = tk.Label(root, 
            text="🎛️ 参数预设功能演示\n点击预设按钮可以快速设置参数\n🔰新手模式 🎯专业模式 🔄恢复默认",
            font=("Arial", 12), fg="blue", bg="lightyellow")
        info_label.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        # 5秒后关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 网格交易演示失败: {e}")
        return False

def demo_fmacd_presets():
    """演示FMACD参数预设"""
    print("\n🔍 演示FMACD参数预设功能")
    
    try:
        from strategy_tabs import FMACDTab
        
        root = tk.Tk()
        root.title("FMACD参数预设演示")
        root.geometry("1200x800")
        
        class MockMainApp:
            def __init__(self):
                self.strategies = {}
                self.strategy_threads = {}
        
        mock_app = MockMainApp()
        fmacd_tab = FMACDTab(root, mock_app)
        fmacd_tab.frame.pack(fill=tk.BOTH, expand=True)
        
        # 显示预设配置
        presets = fmacd_tab.get_parameter_presets()
        print("✅ FMACD预设配置:")
        for mode, config in presets.items():
            print(f"   {mode}: {len(config)}个参数")
            for param, value in config.items():
                print(f"      {param}: {value}")
        
        # 添加说明标签
        info_label = tk.Label(root, 
            text="🎛️ FMACD参数预设功能演示\n包含完整的交易对选择、杠杆设置、风险管理等功能\n🔰新手模式：保守参数 🎯专业模式：激进参数 🔄恢复默认：平衡参数",
            font=("Arial", 12), fg="blue", bg="lightyellow")
        info_label.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        # 5秒后关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ FMACD演示失败: {e}")
        return False

def demo_all_strategy_presets():
    """演示所有策略的参数预设"""
    print("\n🔍 演示所有策略参数预设配置")
    
    strategy_classes = [
        ('GridTradingTab', '网格交易'),
        ('MovingAverageTab', '移动平均线'),
        ('RSIStrategyTab', 'RSI策略'),
        ('FMACDTab', 'FMACD策略'),
        ('AwesomeOscillatorTab', 'AO指标'),
        ('VolumeBreakoutTab', '成交量突破'),
        ('SmartGridTab', '智能网格')
    ]
    
    for class_name, display_name in strategy_classes:
        try:
            exec(f"from strategy_tabs import {class_name}")
            
            # 创建临时实例
            root = tk.Tk()
            root.withdraw()
            
            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
            
            mock_app = MockMainApp()
            strategy_class = eval(class_name)
            strategy_tab = strategy_class(root, mock_app)
            
            # 获取预设配置
            presets = strategy_tab.get_parameter_presets()
            
            print(f"✅ {display_name}策略预设:")
            for mode, config in presets.items():
                mode_name = {'novice': '新手模式', 'professional': '专业模式', 'default': '默认模式'}[mode]
                print(f"   {mode_name}: {len(config)}个参数")
                
                # 显示关键参数
                key_params = list(config.keys())[:3]  # 显示前3个参数
                for param in key_params:
                    print(f"      {param}: {config[param]}")
                if len(config) > 3:
                    print(f"      ... 还有{len(config)-3}个参数")
            
            root.destroy()
            
        except Exception as e:
            print(f"❌ {display_name}策略预设获取失败: {e}")

def demo_preset_features():
    """演示预设功能特性"""
    print("\n🔍 演示参数预设功能特性")
    
    try:
        from strategy_tabs import BaseStrategyTab
        
        root = tk.Tk()
        root.withdraw()
        
        class MockMainApp:
            def __init__(self):
                self.strategies = {}
                self.strategy_threads = {}
        
        mock_app = MockMainApp()
        base_tab = BaseStrategyTab(root, mock_app)
        
        # 测试预设名称获取
        print("✅ 预设模式名称:")
        for preset_type in ['novice', 'professional', 'default']:
            name = base_tab.get_preset_name(preset_type)
            print(f"   {preset_type} -> {name}")
        
        # 测试预设描述
        print("\n✅ 预设模式描述:")
        for preset_type in ['novice', 'professional', 'default']:
            description = base_tab.get_preset_description(preset_type)
            print(f"   {preset_type}:")
            for line in description.split('\\n'):
                if line.strip():
                    print(f"      {line}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 预设功能特性演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("🚀 参数预设功能演示")
    print("=" * 60)
    
    # 演示所有策略的预设配置
    demo_all_strategy_presets()
    
    # 演示预设功能特性
    demo_preset_features()
    
    print("\n" + "=" * 60)
    print("🎉 参数预设功能演示完成！")
    
    print("\n✅ 参数预设功能特点:")
    print("   🔰 新手模式：保守参数，适合初学者")
    print("      • 较大的止损范围(3-5%)")
    print("      • 较小的仓位(5-10%)")
    print("      • 保守的技术指标参数")
    print("      • 降低交易频率")
    
    print("\n   🎯 专业模式：激进参数，适合有经验的交易者")
    print("      • 较小的止损范围(1-2%)")
    print("      • 较大的仓位(15-25%)")
    print("      • 激进的技术指标参数")
    print("      • 增加交易频率")
    
    print("\n   🔄 恢复默认：平衡参数，适合大多数用户")
    print("      • 平衡的风险收益比")
    print("      • 适中的仓位配置")
    print("      • 经过优化的技术参数")
    
    print("\n🎨 用户体验优化:")
    print("   • 一键设置参数")
    print("   • 参数变更确认对话框")
    print("   • 参数对比显示")
    print("   • 工具提示说明")
    print("   • 风险提示")
    
    # 询问是否运行GUI演示
    try:
        choice = input("\n是否运行GUI演示？(y/n): ").lower().strip()
        if choice == 'y':
            print("\n🖥️ 运行GUI演示...")
            demo_grid_trading_presets()
            demo_fmacd_presets()
            print("✅ GUI演示完成")
    except:
        print("跳过GUI演示")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
