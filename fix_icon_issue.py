#!/usr/bin/env python3
"""
修复PyInstaller图标问题
解决找不到mylogo.ico文件的问题
"""

import os
import shutil
import subprocess
import sys

def check_icon_file():
    """检查图标文件"""
    print("检查图标文件...")
    
    icon_files = ['mylogo.ico', 'icon.ico', 'app.ico']
    found_icon = None
    
    for icon in icon_files:
        if os.path.exists(icon):
            print(f"✓ 找到图标文件: {icon}")
            found_icon = icon
            break
        else:
            print(f"✗ 未找到: {icon}")
    
    return found_icon

def create_simple_icon():
    """创建一个简单的图标文件"""
    print("创建默认图标...")
    
    # 创建一个简单的ICO文件内容（16x16像素，单色）
    ico_content = bytes([
        # ICO文件头
        0x00, 0x00,  # 保留字段
        0x01, 0x00,  # 图像类型 (1 = ICO)
        0x01, 0x00,  # 图像数量
        
        # 图像目录条目
        0x10,        # 宽度 (16)
        0x10,        # 高度 (16)
        0x00,        # 颜色数量 (0 = 256色以上)
        0x00,        # 保留字段
        0x01, 0x00,  # 颜色平面数
        0x20, 0x00,  # 每像素位数 (32位)
        0x68, 0x04, 0x00, 0x00,  # 图像数据大小
        0x16, 0x00, 0x00, 0x00,  # 图像数据偏移
        
        # BMP信息头
        0x28, 0x00, 0x00, 0x00,  # 信息头大小
        0x10, 0x00, 0x00, 0x00,  # 图像宽度
        0x20, 0x00, 0x00, 0x00,  # 图像高度 (包括掩码)
        0x01, 0x00,              # 颜色平面数
        0x20, 0x00,              # 每像素位数
        0x00, 0x00, 0x00, 0x00,  # 压缩方式
        0x00, 0x04, 0x00, 0x00,  # 图像数据大小
        0x00, 0x00, 0x00, 0x00,  # 水平分辨率
        0x00, 0x00, 0x00, 0x00,  # 垂直分辨率
        0x00, 0x00, 0x00, 0x00,  # 颜色数量
        0x00, 0x00, 0x00, 0x00,  # 重要颜色数量
    ])
    
    # 添加简单的像素数据 (16x16 蓝色图标)
    pixel_data = b'\x00\x80\xFF\xFF' * 256  # 蓝色像素
    mask_data = b'\x00' * 32  # 透明掩码
    
    ico_content += pixel_data + mask_data
    
    try:
        with open('app_icon.ico', 'wb') as f:
            f.write(ico_content)
        print("✓ 已创建默认图标: app_icon.ico")
        return 'app_icon.ico'
    except Exception as e:
        print(f"✗ 创建图标失败: {e}")
        return None

def fix_spec_file():
    """修复spec文件中的图标路径"""
    print("修复spec文件...")
    
    spec_files = ['MyApp.spec', 'main.spec', 'QuantTradingSystem.spec']
    
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            print(f"处理 {spec_file}...")
            
            try:
                with open(spec_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 移除图标引用或使用存在的图标
                if 'mylogo.ico' in content:
                    icon_file = check_icon_file()
                    if icon_file:
                        content = content.replace('mylogo.ico', icon_file)
                        print(f"✓ 已更新图标路径为: {icon_file}")
                    else:
                        # 移除图标引用
                        content = content.replace("icon='mylogo.ico'", "icon=None")
                        content = content.replace('icon="mylogo.ico"', 'icon=None')
                        print("✓ 已移除图标引用")
                
                with open(spec_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✓ 已修复 {spec_file}")
                return spec_file
                
            except Exception as e:
                print(f"✗ 修复 {spec_file} 失败: {e}")
    
    return None

def build_without_icon():
    """不使用图标进行构建"""
    print("开始无图标构建...")
    
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed", 
            "--name=QuantTradingSystem",
            "--clean",
            "main.py"
        ]
        
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功!")
            return True
        else:
            print("✗ 构建失败:")
            print("错误输出:", result.stderr[-500:])  # 显示最后500字符
            return False
            
    except Exception as e:
        print(f"✗ 构建异常: {e}")
        return False

def build_with_spec():
    """使用修复后的spec文件构建"""
    print("使用spec文件构建...")
    
    spec_file = fix_spec_file()
    if not spec_file:
        print("✗ 没有找到可用的spec文件")
        return False
    
    try:
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
        
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功!")
            return True
        else:
            print("✗ 构建失败:")
            print("错误输出:", result.stderr[-500:])
            return False
            
    except Exception as e:
        print(f"✗ 构建异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("PyInstaller图标问题修复工具")
    print("=" * 60)
    
    # 1. 检查图标文件
    icon_file = check_icon_file()
    
    # 2. 如果没有图标文件，创建一个或选择不使用图标
    if not icon_file:
        print("\n没有找到图标文件，选择解决方案:")
        print("1. 创建默认图标")
        print("2. 不使用图标构建")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            icon_file = create_simple_icon()
        elif choice == "2":
            icon_file = None
        else:
            print("无效选择，将不使用图标构建")
            icon_file = None
    
    # 3. 清理之前的构建
    print("\n清理之前的构建...")
    for dir_name in ['build', 'dist', '__pycache__']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理: {dir_name}")
    
    # 4. 尝试构建
    print("\n开始构建...")
    
    # 首先尝试使用spec文件
    if os.path.exists('MyApp.spec'):
        print("尝试使用现有的spec文件...")
        if build_with_spec():
            print("\n🎉 使用spec文件构建成功!")
        else:
            print("\nspec文件构建失败，尝试直接构建...")
            if build_without_icon():
                print("\n🎉 直接构建成功!")
            else:
                print("\n❌ 所有构建方法都失败了")
    else:
        # 直接构建
        if build_without_icon():
            print("\n🎉 构建成功!")
        else:
            print("\n❌ 构建失败")
    
    # 5. 检查结果
    exe_files = []
    if os.path.exists('dist'):
        for file in os.listdir('dist'):
            if file.endswith('.exe'):
                exe_files.append(os.path.join('dist', file))
    
    if exe_files:
        print(f"\n✅ 找到可执行文件:")
        for exe in exe_files:
            size = os.path.getsize(exe)
            print(f"  📁 {exe} ({size:,} 字节)")
        
        print(f"\n🚀 您可以运行: {exe_files[0]}")
    else:
        print("\n❌ 没有找到生成的可执行文件")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
