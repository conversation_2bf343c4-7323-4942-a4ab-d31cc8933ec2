#!/usr/bin/env python3
"""
策略调试测试
专门用于调试策略初始化问题
"""

import sys
import os
from unittest.mock import Mock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_strategy_initialization():
    """调试策略初始化"""
    print("🔍 调试策略初始化问题...")
    
    # 创建模拟交易所
    mock_exchange = Mock()
    mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
    mock_exchange.create_limit_buy_order.return_value = {'id': 'test_buy_123', 'status': 'open'}
    mock_exchange.create_limit_sell_order.return_value = {'id': 'test_sell_123', 'status': 'open'}
    
    print("\n1. 测试网格交易策略...")
    try:
        from strategies import GridTrading
        print("  ✅ 成功导入 GridTrading")
        
        strategy = GridTrading(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            base_price=0.21375,
            grid_spacing=1.0,
            grid_count=5,
            order_amount=100
        )
        print("  ✅ 网格交易策略初始化成功")
        print(f"    - 交易对: {strategy.symbol}")
        print(f"    - 基准价格: {strategy.base_price}")
        print(f"    - 网格间距: {strategy.grid_spacing}")
        print(f"    - 网格数量: {strategy.grid_count}")
        
    except Exception as e:
        print(f"  ❌ 网格交易策略初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n2. 测试移动平均线策略...")
    try:
        from strategies import MovingAverageStrategy
        print("  ✅ 成功导入 MovingAverageStrategy")
        
        strategy = MovingAverageStrategy(
            exchange=mock_exchange,
            symbol='BTC/USDT',
            short_period=10,
            long_period=30,
            amount=100
        )
        print("  ✅ 移动平均线策略初始化成功")
        print(f"    - 交易对: {strategy.symbol}")
        print(f"    - 短期周期: {strategy.short_period}")
        print(f"    - 长期周期: {strategy.long_period}")
        
    except Exception as e:
        print(f"  ❌ 移动平均线策略初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n3. 测试RSI策略...")
    try:
        from strategies import RSIStrategy
        print("  ✅ 成功导入 RSIStrategy")
        
        strategy = RSIStrategy(
            exchange=mock_exchange,
            symbol='ETH/USDT',
            period=14,
            oversold=30,
            overbought=70,
            amount=100
        )
        print("  ✅ RSI策略初始化成功")
        print(f"    - 交易对: {strategy.symbol}")
        print(f"    - RSI周期: {strategy.period}")
        
    except Exception as e:
        print(f"  ❌ RSI策略初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n4. 测试错误处理模块...")
    try:
        from error_handler import error_handler, ErrorCode
        print("  ✅ 成功导入 error_handler 和 ErrorCode")
        
        # 测试错误代码
        print(f"    - SYSTEM_001: {ErrorCode.SYSTEM_001}")
        print(f"    - NETWORK_001: {ErrorCode.NETWORK_001}")
        
        # 测试错误处理器方法
        print(f"    - handle_error 方法存在: {hasattr(error_handler, 'handle_error')}")
        
    except Exception as e:
        print(f"  ❌ 错误处理模块测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n5. 测试用户友好消息...")
    try:
        from user_friendly_messages import user_messages
        print("  ✅ 成功导入 user_messages")
        
        # 测试方法存在性
        print(f"    - show_error 方法存在: {hasattr(user_messages, 'show_error')}")
        print(f"    - show_warning 方法存在: {hasattr(user_messages, 'show_warning')}")
        
    except Exception as e:
        print(f"  ❌ 用户友好消息测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_parameter_validation():
    """测试参数验证"""
    print("\n6. 测试参数验证...")
    try:
        from parameter_validator import validator
        print("  ✅ 成功导入 validator")
        
        # 测试有效参数
        validator.validate_symbol('CFX/USDT')
        print("    ✅ CFX/USDT 验证通过")
        
        validator.validate_symbol('CFX-USDT-SWAP')
        print("    ✅ CFX-USDT-SWAP 验证通过")
        
        validator.validate_price(0.21375)
        print("    ✅ 价格验证通过")
        
        validator.validate_amount(100)
        print("    ✅ 数量验证通过")
        
        # 测试无效参数
        try:
            validator.validate_symbol('INVALID_SYMBOL')
            print("    ❌ 无效符号应该被拒绝")
        except:
            print("    ✅ 无效符号正确被拒绝")
        
    except Exception as e:
        print(f"  ❌ 参数验证测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始策略调试测试")
    print("=" * 50)
    
    debug_strategy_initialization()
    test_parameter_validation()
    
    print("\n" + "=" * 50)
    print("🏁 调试测试完成")

if __name__ == "__main__":
    main()
