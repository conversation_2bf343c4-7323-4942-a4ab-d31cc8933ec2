"""
配置管理模块
处理API密钥、交易参数等配置信息的加密存储和读取
"""
import os
import json
import base64
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import tkinter.messagebox as messagebox

class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = {}
        self.key = None
        self.load_config()
    
    def generate_key(self, password: str) -> bytes:
        """从密码生成加密密钥"""
        try:
            from cryptography.hazmat.backends import default_backend
            password_bytes = password.encode()
            salt = b'trading_system_salt'  # 在实际应用中应该使用随机盐
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend()
            )
            key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
            return key
        except Exception as e:
            # 如果加密库有问题，使用简单的base64编码作为备选
            import hashlib
            key = hashlib.sha256(password.encode()).digest()
            return base64.urlsafe_b64encode(key)
    
    def set_password(self, password: str):
        """设置加密密码"""
        self.key = self.generate_key(password)
    
    def encrypt_data(self, data: str) -> str:
        """加密数据"""
        if not self.key:
            raise ValueError("未设置加密密码")
        f = Fernet(self.key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        if not self.key:
            raise ValueError("未设置加密密码")
        try:
            f = Fernet(self.key)
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            raise ValueError("解密失败，密码可能错误")
    
    def save_api_credentials(self, exchange: str, api_key: str, secret: str, passphrase: str = ""):
        """保存API凭证（加密）"""
        if not self.key:
            raise ValueError("未设置加密密码")
        
        credentials = {
            "api_key": api_key,
            "secret": secret,
            "passphrase": passphrase
        }
        
        encrypted_credentials = self.encrypt_data(json.dumps(credentials))
        
        if "exchanges" not in self.config:
            self.config["exchanges"] = {}
        
        self.config["exchanges"][exchange] = encrypted_credentials
        self.save_config()
    
    def get_api_credentials(self, exchange: str) -> dict:
        """获取API凭证（解密）"""
        if "exchanges" not in self.config or exchange not in self.config["exchanges"]:
            return {}
        
        try:
            encrypted_data = self.config["exchanges"][exchange]
            decrypted_data = self.decrypt_data(encrypted_data)
            return json.loads(decrypted_data)
        except Exception as e:
            messagebox.showerror("错误", f"获取{exchange}API凭证失败: {str(e)}")
            return {}
    
    def save_strategy_config(self, strategy_name: str, config: dict):
        """保存策略配置"""
        if "strategies" not in self.config:
            self.config["strategies"] = {}
        
        self.config["strategies"][strategy_name] = config
        self.save_config()
    
    def get_strategy_config(self, strategy_name: str) -> dict:
        """获取策略配置"""
        if "strategies" not in self.config:
            return {}
        
        return self.config["strategies"].get(strategy_name, {})
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def load_config(self):
        """从文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")
                self.config = {}
        else:
            self.config = {}

# 默认配置
DEFAULT_CONFIG = {
    "exchanges": {
        "binance": {
            "name": "Binance",
            "sandbox_url": "https://testnet.binance.vision",
            "supported_symbols": ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "DOT/USDT"]
        },
        "okx": {
            "name": "OKX",
            "sandbox_url": "https://www.okx.com",
            "supported_symbols": ["BTC/USDT", "ETH/USDT", "OKB/USDT", "ADA/USDT", "DOT/USDT"]
        },
        "huobi": {
            "name": "Huobi",
            "sandbox_url": "https://api.huobi.pro",
            "supported_symbols": ["BTC/USDT", "ETH/USDT", "HT/USDT", "ADA/USDT", "DOT/USDT"]
        }
    },
    "risk_management": {
        "max_daily_loss": 0.02,
        "max_position_size": 0.1,
        "max_drawdown": 0.15,
        "stop_loss_pct": 0.03,
        "take_profit_pct": 0.06
    },
    "strategies": {
        "grid_trading": {
            "base_price": 30000,
            "grid_spacing": 1.0,
            "grid_count": 10,
            "order_amount": 0.001
        },
        "moving_average": {
            "short_period": 10,
            "long_period": 30,
            "timeframe": "1h"
        },
        "rsi_strategy": {
            "period": 14,
            "oversold": 30,
            "overbought": 70,
            "timeframe": "1h"
        },
        "volume_breakout": {
            "lookback_period": 20,
            "volume_multiplier": 2.0,
            "breakout_threshold": 0.02,
            "timeframe": "1h"
        },
        "smart_grid": {
            "initial_investment": 1000,
            "base_grid_spacing": 0.01,
            "grid_count": 10,
            "rebalance_hours": 2
        }
    }
}
