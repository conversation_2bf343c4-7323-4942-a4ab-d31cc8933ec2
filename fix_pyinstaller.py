#!/usr/bin/env python3
"""
PyInstaller打包问题修复脚本
解决Python版本冲突和依赖问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    print(f"当前Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查是否有多个Python版本
    python_paths = []
    for path in os.environ.get('PATH', '').split(os.pathsep):
        python_exe = os.path.join(path, 'python.exe')
        if os.path.exists(python_exe):
            python_paths.append(python_exe)
    
    if len(python_paths) > 1:
        print("⚠️ 检测到多个Python安装:")
        for i, path in enumerate(python_paths, 1):
            print(f"  {i}. {path}")
        return False
    return True

def clean_pyinstaller_cache():
    """清理PyInstaller缓存"""
    print("\n清理PyInstaller缓存...")
    
    cache_dirs = [
        os.path.expanduser("~\\AppData\\Roaming\\pyinstaller"),
        os.path.expanduser("~\\AppData\\Local\\pyinstaller"),
        "build",
        "dist",
        "__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"✓ 已清理: {cache_dir}")
            except Exception as e:
                print(f"✗ 清理失败 {cache_dir}: {e}")
        else:
            print(f"- 不存在: {cache_dir}")

def reinstall_pyinstaller():
    """重新安装PyInstaller"""
    print("\n重新安装PyInstaller...")
    
    try:
        # 卸载PyInstaller
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "pyinstaller", "-y"], 
                      check=True, capture_output=True)
        print("✓ PyInstaller已卸载")
        
        # 重新安装PyInstaller
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True, capture_output=True)
        print("✓ PyInstaller已重新安装")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller重新安装失败: {e}")
        return False

def create_spec_file():
    """创建优化的spec文件"""
    print("\n创建优化的spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'ccxt',
        'pandas',
        'numpy',
        'requests',
        'cryptography',
        'threading',
        'queue',
        'json',
        'time',
        'datetime',
        'logging',
        'configparser',
        'sqlite3',
        'hashlib',
        'hmac',
        'base64',
        'urllib3',
        'websocket',
        'ssl'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'pytest',
        'setuptools'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='QuantTradingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('QuantTradingSystem.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建 QuantTradingSystem.spec")

def check_dependencies():
    """检查关键依赖"""
    print("\n检查关键依赖...")
    
    required_packages = [
        'tkinter',
        'ccxt', 
        'pandas',
        'numpy',
        'requests',
        'cryptography'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - 缺失")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装缺失的包: {missing_packages}")
        return False
    return True

def build_executable():
    """构建可执行文件"""
    print("\n开始构建可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "QuantTradingSystem.spec"]
        
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功!")
            print("可执行文件位置: dist/QuantTradingSystem.exe")
            return True
        else:
            print("✗ 构建失败:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建异常: {e}")
        return False

def create_alternative_build_script():
    """创建备用构建脚本"""
    print("\n创建备用构建脚本...")
    
    script_content = '''@echo off
echo 量化交易系统打包脚本
echo ========================

echo 1. 清理缓存...
rmdir /s /q build 2>nul
rmdir /s /q dist 2>nul
rmdir /s /q __pycache__ 2>nul

echo 2. 开始打包...
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name="QuantTradingSystem" ^
    --add-data="*.py;." ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=ccxt ^
    --hidden-import=pandas ^
    --hidden-import=numpy ^
    --hidden-import=requests ^
    --hidden-import=cryptography ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    main.py

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] 打包成功!
    echo 可执行文件: dist\\QuantTradingSystem.exe
) else (
    echo [ERROR] 打包失败!
)

pause
'''
    
    with open('build_exe.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✓ 已创建 build_exe.bat")

def main():
    """主函数"""
    print("=" * 60)
    print("PyInstaller打包问题修复工具")
    print("=" * 60)
    
    # 1. 检查Python版本
    if not check_python_version():
        print("\n⚠️ 建议:")
        print("1. 卸载多余的Python版本")
        print("2. 确保只有一个Python版本在PATH中")
        print("3. 重启命令行后重试")
    
    # 2. 清理缓存
    clean_pyinstaller_cache()
    
    # 3. 检查依赖
    if not check_dependencies():
        print("\n请先安装缺失的依赖包")
        return
    
    # 4. 重新安装PyInstaller
    if not reinstall_pyinstaller():
        print("\n请手动重新安装PyInstaller:")
        print("pip uninstall pyinstaller -y")
        print("pip install pyinstaller")
        return
    
    # 5. 创建spec文件
    create_spec_file()
    
    # 6. 创建备用脚本
    create_alternative_build_script()
    
    # 7. 尝试构建
    print("\n" + "=" * 60)
    choice = input("是否现在尝试构建? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        if build_executable():
            print("\n🎉 构建成功!")
        else:
            print("\n❌ 构建失败，请尝试运行 build_exe.bat")
    
    print("\n" + "=" * 60)
    print("修复完成!")
    print("如果仍有问题，请尝试:")
    print("1. 运行 build_exe.bat")
    print("2. 使用虚拟环境")
    print("3. 升级到Python 3.9+")
    print("=" * 60)

if __name__ == "__main__":
    main()
