#!/usr/bin/env python3
"""
详细Bug诊断工具
专门诊断失败的测试用例
"""

import sys
import os
import traceback
from unittest.mock import Mock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_grid_trading_strategy():
    """详细测试网格交易策略"""
    print("🔍 详细测试网格交易策略...")
    
    try:
        from strategies import GridTrading
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        mock_exchange.create_limit_buy_order.return_value = {'id': 'buy_123', 'status': 'open'}
        mock_exchange.create_limit_sell_order.return_value = {'id': 'sell_123', 'status': 'open'}
        
        print("  ✅ 模拟交易所创建成功")
        
        # 创建网格策略
        strategy = GridTrading(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            base_price=0.21375,
            grid_spacing=1.0,
            grid_count=10,
            order_amount=100
        )
        
        print("  ✅ 网格策略创建成功")
        print(f"     - 交易对: {strategy.symbol}")
        print(f"     - 基准价格: {strategy.base_price}")
        print(f"     - 网格间距: {strategy.grid_spacing}%")
        print(f"     - 网格数量: {strategy.grid_count}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 网格策略测试失败:")
        print(f"     错误类型: {type(e).__name__}")
        print(f"     错误信息: {str(e)}")
        print("     详细堆栈:")
        traceback.print_exc()
        return False

def test_moving_average_strategy():
    """详细测试移动平均线策略"""
    print("\n🔍 详细测试移动平均线策略...")
    
    try:
        from strategies import MovingAverageStrategy
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        
        print("  ✅ 模拟交易所创建成功")
        
        # 创建MA策略
        strategy = MovingAverageStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_window=10,
            long_window=30,
            amount=100
        )
        
        print("  ✅ 移动平均线策略创建成功")
        print(f"     - 交易对: {strategy.symbol}")
        print(f"     - 短期窗口: {strategy.short_window}")
        print(f"     - 长期窗口: {strategy.long_window}")
        print(f"     - 交易数量: {strategy.amount}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 移动平均线策略测试失败:")
        print(f"     错误类型: {type(e).__name__}")
        print(f"     错误信息: {str(e)}")
        print("     详细堆栈:")
        traceback.print_exc()
        return False

def test_rsi_strategy():
    """详细测试RSI策略"""
    print("\n🔍 详细测试RSI策略...")
    
    try:
        from strategies import RSIStrategy
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        
        print("  ✅ 模拟交易所创建成功")
        
        # 创建RSI策略
        strategy = RSIStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            rsi_period=14,
            oversold_threshold=30,
            overbought_threshold=70,
            amount=100
        )
        
        print("  ✅ RSI策略创建成功")
        print(f"     - 交易对: {strategy.symbol}")
        print(f"     - RSI周期: {strategy.rsi_period}")
        print(f"     - 超卖阈值: {strategy.oversold_threshold}")
        print(f"     - 超买阈值: {strategy.overbought_threshold}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ RSI策略测试失败:")
        print(f"     错误类型: {type(e).__name__}")
        print(f"     错误信息: {str(e)}")
        print("     详细堆栈:")
        traceback.print_exc()
        return False

def test_user_friendly_messages():
    """详细测试用户友好消息"""
    print("\n🔍 详细测试用户友好消息...")
    
    try:
        from user_friendly_messages import get_user_friendly_message, MESSAGE_TEMPLATES
        
        print("  ✅ 用户友好消息模块导入成功")
        
        # 测试消息模板
        if MESSAGE_TEMPLATES:
            print(f"     - 消息模板数量: {len(MESSAGE_TEMPLATES)}")
            
            # 测试获取消息
            test_message = get_user_friendly_message("connection_failed", {"exchange": "Gate.io"})
            print(f"     - 测试消息: {test_message[:50]}...")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 用户友好消息测试失败:")
        print(f"     错误类型: {type(e).__name__}")
        print(f"     错误信息: {str(e)}")
        print("     详细堆栈:")
        traceback.print_exc()
        return False

def test_ao_strategy():
    """详细测试AO策略"""
    print("\n🔍 详细测试AO策略...")
    
    try:
        from strategies import AwesomeOscillatorStrategy
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        
        print("  ✅ 模拟交易所创建成功")
        
        # 创建AO策略
        strategy = AwesomeOscillatorStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=5,
            long_period=34,
            amount=100
        )
        
        print("  ✅ AO策略创建成功")
        print(f"     - 交易对: {strategy.symbol}")
        print(f"     - 短期周期: {strategy.short_period}")
        print(f"     - 长期周期: {strategy.long_period}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AO策略测试失败:")
        print(f"     错误类型: {type(e).__name__}")
        print(f"     错误信息: {str(e)}")
        print("     详细堆栈:")
        traceback.print_exc()
        return False

def test_fmacd_strategy():
    """详细测试FMACD策略"""
    print("\n🔍 详细测试FMACD策略...")
    
    try:
        from strategies import FMACDStrategy
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        
        print("  ✅ 模拟交易所创建成功")
        
        # 创建FMACD策略
        strategy = FMACDStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            fast_period=12,
            slow_period=26,
            signal_period=9,
            amount=100
        )
        
        print("  ✅ FMACD策略创建成功")
        print(f"     - 交易对: {strategy.symbol}")
        print(f"     - 快速周期: {strategy.fast_period}")
        print(f"     - 慢速周期: {strategy.slow_period}")
        print(f"     - 信号周期: {strategy.signal_period}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ FMACD策略测试失败:")
        print(f"     错误类型: {type(e).__name__}")
        print(f"     错误信息: {str(e)}")
        print("     详细堆栈:")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始详细Bug诊断")
    print("=" * 60)
    
    tests = [
        ("网格交易策略", test_grid_trading_strategy),
        ("移动平均线策略", test_moving_average_strategy),
        ("RSI策略", test_rsi_strategy),
        ("用户友好消息", test_user_friendly_messages),
        ("AO策略", test_ao_strategy),
        ("FMACD策略", test_fmacd_strategy),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 诊断结果: {passed}/{total} 通过")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 发现问题，需要修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
