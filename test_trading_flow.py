#!/usr/bin/env python3
"""
交易流程全面测试
测试完整的交易流程，包括连接、数据获取、订单管理、余额查询
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_framework import test_framework, TestResult
from exchange_manager import ExchangeManager
from environment_manager import environment_manager

class TradingFlowTester:
    """交易流程测试器"""
    
    def __init__(self):
        self.framework = test_framework
        self.exchange_manager = ExchangeManager()
        self.mock_exchange = self.framework.create_mock_exchange()
        
    def test_exchange_connection(self):
        """测试交易所连接功能"""
        
        def test_exchange_manager_initialization():
            """测试交易所管理器初始化"""
            manager = ExchangeManager()
            self.framework.assert_not_none(manager, "交易所管理器创建失败")
            self.framework.assert_true(hasattr(manager, 'supported_exchanges'), "应该有支持的交易所列表")
            self.framework.assert_true(len(manager.supported_exchanges) > 0, "应该支持至少一个交易所")
        
        def test_supported_exchanges():
            """测试支持的交易所"""
            supported = self.exchange_manager.supported_exchanges
            expected_exchanges = ['binance', 'okx', 'huobi', 'gate']
            
            for exchange in expected_exchanges:
                self.framework.assert_true(exchange in supported, f"应该支持 {exchange} 交易所")
                
                # 检查交易所配置
                config = supported[exchange]
                self.framework.assert_true('class' in config, f"{exchange} 应该有交易所类配置")
                self.framework.assert_true('name' in config, f"{exchange} 应该有名称配置")
                self.framework.assert_true('test_symbol' in config, f"{exchange} 应该有测试交易对配置")
        
        def test_connection_parameters():
            """测试连接参数验证"""
            # 测试无效交易所名称
            try:
                result = self.exchange_manager.connect_exchange(
                    'invalid_exchange', 'test_key', 'test_secret'
                )
                self.framework.assert_false(result, "无效交易所名称应该连接失败")
            except Exception:
                pass  # 预期会抛出异常
            
            # 测试空API密钥
            try:
                result = self.exchange_manager.connect_exchange(
                    'okx', '', '', ''
                )
                # 在模拟模式下可能会成功，这是正常的
                self.framework.logger.info("空API密钥连接测试完成")
            except Exception as e:
                self.framework.logger.info(f"空API密钥连接测试: {e}")
        
        # 运行连接测试
        self.framework.run_test_case(
            test_exchange_manager_initialization,
            "交易所管理器初始化测试",
            "验证交易所管理器的正确初始化",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_supported_exchanges,
            "支持的交易所测试",
            "验证系统支持的交易所配置",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_connection_parameters,
            "连接参数验证测试",
            "验证连接参数的有效性检查",
            "交易流程测试",
            "MEDIUM"
        )
    
    def test_market_data_retrieval(self):
        """测试市场数据获取"""
        
        def test_ticker_data():
            """测试价格数据获取"""
            ticker = self.mock_exchange.fetch_ticker('CFX/USDT')
            
            self.framework.assert_not_none(ticker, "价格数据不应为空")
            self.framework.assert_true('symbol' in ticker, "应该包含交易对信息")
            self.framework.assert_true('last' in ticker, "应该包含最新价格")
            self.framework.assert_true('bid' in ticker, "应该包含买一价")
            self.framework.assert_true('ask' in ticker, "应该包含卖一价")
            self.framework.assert_true('timestamp' in ticker, "应该包含时间戳")
            
            # 验证价格数据的合理性
            self.framework.assert_true(ticker['last'] > 0, "最新价格应大于0")
            self.framework.assert_true(ticker['bid'] > 0, "买一价应大于0")
            self.framework.assert_true(ticker['ask'] > 0, "卖一价应大于0")
            self.framework.assert_true(ticker['ask'] >= ticker['bid'], "卖一价应大于等于买一价")
        
        def test_balance_data():
            """测试余额数据获取"""
            balance = self.mock_exchange.fetch_balance()
            
            self.framework.assert_not_none(balance, "余额数据不应为空")
            self.framework.assert_true('USDT' in balance, "应该包含USDT余额")
            self.framework.assert_true('CFX' in balance, "应该包含CFX余额")
            
            # 验证余额数据结构
            usdt_balance = balance['USDT']
            self.framework.assert_true('free' in usdt_balance, "应该包含可用余额")
            self.framework.assert_true('used' in usdt_balance, "应该包含冻结余额")
            self.framework.assert_true('total' in usdt_balance, "应该包含总余额")
            
            # 验证余额数据的合理性
            self.framework.assert_true(usdt_balance['free'] >= 0, "可用余额应大于等于0")
            self.framework.assert_true(usdt_balance['used'] >= 0, "冻结余额应大于等于0")
            self.framework.assert_true(usdt_balance['total'] >= 0, "总余额应大于等于0")
        
        def test_data_consistency():
            """测试数据一致性"""
            # 多次获取数据，验证一致性
            ticker1 = self.mock_exchange.fetch_ticker('CFX/USDT')
            time.sleep(0.1)
            ticker2 = self.mock_exchange.fetch_ticker('CFX/USDT')
            
            # 在模拟环境下，价格可能会有小幅变化，这是正常的
            self.framework.assert_true(abs(ticker1['last'] - ticker2['last']) < 0.01, 
                                     "短时间内价格变化应该较小")
        
        # 运行市场数据测试
        self.framework.run_test_case(
            test_ticker_data,
            "价格数据获取测试",
            "验证价格数据的获取和格式",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_balance_data,
            "余额数据获取测试",
            "验证余额数据的获取和格式",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_data_consistency,
            "数据一致性测试",
            "验证市场数据的一致性",
            "交易流程测试",
            "MEDIUM"
        )
    
    def test_order_management(self):
        """测试订单管理功能"""
        
        def test_market_order_creation():
            """测试市价单创建"""
            # 测试市价买单
            buy_order = self.mock_exchange.create_market_buy_order('CFX/USDT', 100)
            
            self.framework.assert_not_none(buy_order, "市价买单创建失败")
            self.framework.assert_equal(buy_order['symbol'], 'CFX/USDT', "交易对错误")
            self.framework.assert_equal(buy_order['type'], 'market', "订单类型错误")
            self.framework.assert_equal(buy_order['side'], 'buy', "订单方向错误")
            self.framework.assert_equal(buy_order['amount'], 100, "订单数量错误")
            self.framework.assert_true('id' in buy_order, "应该包含订单ID")
            self.framework.assert_true('timestamp' in buy_order, "应该包含时间戳")
        
        def test_limit_order_creation():
            """测试限价单创建"""
            # 测试限价买单
            limit_order = self.mock_exchange.create_limit_buy_order('CFX/USDT', 100, 0.21)
            
            self.framework.assert_not_none(limit_order, "限价买单创建失败")
            self.framework.assert_equal(limit_order['symbol'], 'CFX/USDT', "交易对错误")
            self.framework.assert_equal(limit_order['type'], 'limit', "订单类型错误")
            self.framework.assert_equal(limit_order['side'], 'buy', "订单方向错误")
            self.framework.assert_equal(limit_order['amount'], 100, "订单数量错误")
            self.framework.assert_equal(limit_order['price'], 0.21, "订单价格错误")
            self.framework.assert_equal(limit_order['status'], 'open', "限价单状态应为open")
        
        def test_order_query():
            """测试订单查询"""
            # 创建订单
            order = self.mock_exchange.create_limit_buy_order('CFX/USDT', 100, 0.21)
            order_id = order['id']
            
            # 查询订单
            queried_order = self.mock_exchange.fetch_order(order_id, 'CFX/USDT')
            
            self.framework.assert_not_none(queried_order, "订单查询失败")
            self.framework.assert_equal(queried_order['id'], order_id, "查询的订单ID不匹配")
            self.framework.assert_equal(queried_order['symbol'], 'CFX/USDT', "查询的交易对不匹配")
        
        def test_order_cancellation():
            """测试订单取消"""
            # 创建限价单
            order = self.mock_exchange.create_limit_buy_order('CFX/USDT', 100, 0.21)
            order_id = order['id']
            
            # 取消订单
            canceled_order = self.mock_exchange.cancel_order(order_id, 'CFX/USDT')
            
            self.framework.assert_not_none(canceled_order, "订单取消失败")
            self.framework.assert_equal(canceled_order['id'], order_id, "取消的订单ID不匹配")
            self.framework.assert_equal(canceled_order['status'], 'canceled', "订单状态应为canceled")
        
        # 运行订单管理测试
        self.framework.run_test_case(
            test_market_order_creation,
            "市价单创建测试",
            "验证市价单的创建功能",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_limit_order_creation,
            "限价单创建测试",
            "验证限价单的创建功能",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_order_query,
            "订单查询测试",
            "验证订单查询功能",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_order_cancellation,
            "订单取消测试",
            "验证订单取消功能",
            "交易流程测试",
            "HIGH"
        )
    
    def test_risk_management(self):
        """测试风险管理功能"""
        
        def test_balance_validation():
            """测试余额验证"""
            balance = self.mock_exchange.fetch_balance()
            usdt_balance = balance['USDT']['free']
            
            # 测试余额充足的情况
            if usdt_balance >= 100:
                self.framework.assert_true(True, "余额充足，可以进行交易")
            else:
                self.framework.logger.warning("余额不足，无法进行大额交易")
        
        def test_price_validation():
            """测试价格验证"""
            ticker = self.mock_exchange.fetch_ticker('CFX/USDT')
            current_price = ticker['last']
            
            # 验证价格在合理范围内
            self.framework.assert_in_range(current_price, 0.01, 10.0, 
                                         "CFX价格应在合理范围内")
            
            # 测试价格偏差检查
            bid_ask_spread = ticker['ask'] - ticker['bid']
            spread_percentage = bid_ask_spread / current_price * 100
            
            self.framework.assert_true(spread_percentage < 5.0, 
                                     "买卖价差应小于5%")
        
        # 运行风险管理测试
        self.framework.run_test_case(
            test_balance_validation,
            "余额验证测试",
            "验证交易前的余额检查",
            "交易流程测试",
            "HIGH"
        )
        
        self.framework.run_test_case(
            test_price_validation,
            "价格验证测试",
            "验证价格数据的合理性检查",
            "交易流程测试",
            "MEDIUM"
        )
    
    def run_all_trading_flow_tests(self):
        """运行所有交易流程测试"""
        print("开始交易流程测试...")
        
        # 测试所有交易流程
        self.test_exchange_connection()
        self.test_market_data_retrieval()
        self.test_order_management()
        self.test_risk_management()
        
        print("交易流程测试完成")

def main():
    """主测试函数"""
    print("=" * 80)
    print("量化交易系统 - 交易流程全面测试")
    print("=" * 80)
    
    tester = TradingFlowTester()
    tester.run_all_trading_flow_tests()
    
    # 生成测试报告
    summary = test_framework.reporter.generate_summary()
    print(f"\n交易流程测试摘要:")
    print(f"总测试用例: {summary['total']}")
    print(f"通过: {summary['passed']}")
    print(f"失败: {summary['failed']}")
    print(f"错误: {summary['errors']}")
    print(f"通过率: {summary['pass_rate']:.1f}%")

if __name__ == "__main__":
    main()
