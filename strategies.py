"""
量化交易策略模块

本模块包含了量化交易系统的所有核心交易策略，每种策略都针对不同的市场情况和交易风格设计。
所有策略都经过严格的参数验证和错误处理，确保在各种市场环境下的稳定运行。

包含的策略类型：
1. GridTrading - 网格交易策略：适合震荡市场，通过高抛低吸获取稳定收益
2. MovingAverageStrategy - 移动平均线策略：适合趋势市场，跟随趋势方向交易
3. RSIStrategy - RSI策略：基于相对强弱指标，适合超买超卖信号交易
4. VolumeBreakoutStrategy - 成交量突破策略：基于成交量异常，捕捉价格突破机会

使用说明：
- 所有策略都需要传入交易所对象、交易对、相关参数
- 策略会自动进行参数验证，确保参数的合理性和安全性
- 支持模拟模式和实盘模式，建议先在模拟模式下充分测试
- 每个策略都有详细的日志记录，便于监控和调试

注意事项：
- 量化交易存在风险，请充分了解策略原理后使用
- 建议从小资金开始测试，逐步增加投资规模
- 定期监控策略表现，及时调整参数或停止策略
- 在市场剧烈波动时要特别谨慎

作者：量化交易系统开发团队
版本：v2.0 (增强版，包含参数验证和中文错误处理)
"""
import ccxt
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from logger import get_logger, log_trade, log_error, log_strategy_start, log_strategy_stop
from parameter_validator import validator, ValidationError
from error_handler import error_handler, ErrorCode

class GridTrading:
    """
    网格交易策略类

    网格交易是一种适合震荡市场的量化交易策略。它在当前价格上下设置多个买入和卖出订单，
    形成一个"网格"，当价格在网格范围内波动时，通过高抛低吸来获取收益。

    策略原理：
    1. 以当前价格为基准，按照设定的间距在上下方向设置网格
    2. 在价格下方设置买入订单，在价格上方设置卖出订单
    3. 当价格波动触发订单成交时，在相应位置重新设置反向订单
    4. 通过不断的买低卖高来获取价差收益

    适用场景：
    - 震荡市场：价格在一定范围内波动，没有明显趋势
    - 流动性好的交易对：确保订单能够及时成交
    - 波动率适中：太小无利润，太大风险高

    风险提示：
    - 单边趋势市场可能导致大量亏损
    - 需要充足的资金支持网格运行
    - 网格间距设置不当可能影响收益

    参数说明：
    - exchange: 交易所对象，用于执行交易操作
    - symbol: 交易对，如 'CFX/USDT'
    - base_price: 基准价格，网格的中心价格
    - grid_spacing: 网格间距（百分比），如 2.0 表示 2%
    - grid_count: 网格数量，建议 10-30 个
    - order_amount: 每个网格的交易数量

    使用示例：
    ```python
    # 创建网格交易策略
    strategy = GridTrading(
        exchange=exchange,
        symbol='CFX/USDT',
        base_price=0.213456,
        grid_spacing=2.0,      # 2% 间距
        grid_count=10,         # 10 个网格
        order_amount=100       # 每次交易 100 个代币
    )

    # 启动策略
    strategy.start()

    # 停止策略
    strategy.stop()
    ```
    """

    def __init__(self, exchange, symbol, base_price, grid_spacing, grid_count, order_amount):
        """
        初始化网格交易策略

        Args:
            exchange: 交易所对象，必须实现标准的交易接口
            symbol (str): 交易对符号，格式如 'CFX/USDT'
            base_price (float): 基准价格，作为网格的中心价格
            grid_spacing (float): 网格间距百分比，如 2.0 表示 2%
            grid_count (int): 网格总数量，建议 10-30 个
            order_amount (float): 每个网格的交易数量

        Raises:
            ValidationError: 当参数不符合要求时抛出
            Exception: 当策略初始化失败时抛出

        注意事项：
            - 基准价格建议设置为当前市场价格
            - 网格间距不宜过小（<0.5%）或过大（>10%）
            - 网格数量过多会占用大量资金，过少可能错过机会
            - 交易数量应根据账户资金合理设置
        """
        # 初始化日志记录器
        self.logger = get_logger("grid_trading")

        try:
            # 第一步：严格的参数验证，确保所有参数都在合理范围内
            self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)

            # 第二步：初始化策略属性
            self.exchange = exchange                    # 交易所接口对象
            self.symbol = symbol                        # 交易对，如 'CFX/USDT'
            self.base_price = base_price               # 网格中心价格，通常设为当前市价
            self.grid_spacing = grid_spacing           # 网格间距百分比，如 2.0 表示 2%
            self.grid_count = grid_count               # 网格总数量，决定策略覆盖的价格范围
            self.order_amount = order_amount           # 每个网格的交易数量

            # 第三步：初始化运行状态变量
            self.orders = {}                           # 存储当前活跃的订单信息 {价格: 订单详情}
            self.running = False                       # 策略运行状态标志
            self.last_price = None                     # 缓存最近一次获取的价格，避免频繁API调用
            self.price_check_counter = 0               # 价格检查计数器，用于性能监控

            # 记录成功初始化的日志
            self.logger.info(f"网格交易策略初始化成功: {symbol}, 基准价格: {base_price}, 网格间距: {grid_spacing}%, 网格数量: {grid_count}")

        except ValidationError as e:
            # 参数验证失败的处理
            error_details = error_handler.handle_error(e, context={
                "strategy": "GridTrading",
                "symbol": symbol,
                "base_price": base_price,
                "grid_spacing": grid_spacing,
                "grid_count": grid_count,
                "order_amount": order_amount
            })
            self.logger.error(f"网格交易策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            # 其他初始化错误的处理
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"网格交易策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, base_price, grid_spacing, grid_count, order_amount):
        """
        验证网格交易策略的所有参数

        这个方法会对传入的参数进行严格验证，确保参数在合理范围内，
        避免因参数设置不当导致的策略失败或资金损失。

        Args:
            symbol (str): 交易对符号
            base_price (float): 基准价格
            grid_spacing (float): 网格间距百分比
            grid_count (int): 网格数量
            order_amount (float): 每次交易数量

        Raises:
            ValidationError: 当任何参数不符合要求时抛出

        验证规则：
            - 交易对格式必须正确（如 CFX/USDT）
            - 基准价格必须为正数且在合理范围内
            - 网格间距建议在 0.1% - 20% 之间
            - 网格数量建议在 2 - 100 之间
            - 交易数量必须为正数且不超过限制
        """
        validator.validate_symbol(symbol)
        # 注意：这里将百分比转换为小数，如 2.0% -> 0.02
        validator.validate_grid_parameters(base_price, grid_spacing/100, grid_count, order_amount)
    
    def create_grid_orders(self):
        """
        创建网格订单

        这是网格交易策略的核心方法，会在当前价格上下创建买入和卖出订单，
        形成一个价格网格。当价格波动时，这些订单会被触发，实现高抛低吸。

        工作原理：
        1. 获取当前市场价格作为参考
        2. 以基准价格为中心，按网格间距计算各个网格价格
        3. 在低于当前价格的位置设置买入订单（等待价格下跌时买入）
        4. 在高于当前价格的位置设置卖出订单（等待价格上涨时卖出）

        Returns:
            None

        注意事项：
            - 确保账户有足够的资金支持所有订单
            - 网格订单一旦设置，会持续等待市场价格触发
            - 如果市场出现单边行情，可能导致订单全部在一侧成交
        """
        # 获取当前市场价格作为网格布局的参考
        current_price = self.get_current_price()

        # 计算网格范围：从 -网格数量/2 到 +网格数量/2
        # 例如：10个网格就是 -5 到 +5，跳过 0（当前价格位置）
        for i in range(-self.grid_count//2, self.grid_count//2 + 1):
            if i == 0:
                continue  # 跳过中心位置，不在当前价格设置订单

            # 计算每个网格的价格
            # 公式：网格价格 = 当前价格 × (1 + 网格位置 × 间距百分比 / 100)
            grid_price = current_price * (1 + i * self.grid_spacing / 100)

            if i < 0:  # 负数位置：价格低于当前价格，设置买入订单
                try:
                    order = self.exchange.create_limit_buy_order(
                        self.symbol, self.order_amount, grid_price
                    )
                    self.orders[grid_price] = {'type': 'buy', 'order': order}
                    self.logger.info(f"买单已放置:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"买单失败:{e}")
            else:  # 上方网格，放卖单
                try:
                    order = self.exchange.create_limit_sell_order(
                        self.symbol, self.order_amount, grid_price
                    )
                    self.orders[grid_price] = {'type': 'sell', 'order': order}
                    self.logger.info(f"卖单已放置:{grid_price:.2f}")
                except Exception as e:
                    self.logger.error(f"卖单失败: {e}")
    
    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = price  # 缓存成功获取的价格
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price  # 缓存成功获取的价格
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price  # 缓存成功获取的价格
                return price

            # 如果所有方法都失败，返回默认值并记录错误
            self.logger.error("无法获取有效价格，使用缓存价格")
            return self.last_price if self.last_price else 0.0

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            # 返回上次成功获取的价格，如果没有则返回0
            return self.last_price if self.last_price else 0.0
    
    def check_and_rebalance(self):
        """检查订单状态并重新平衡"""
        for price, order_info in list(self.orders.items()):
            try:
                order_status = self.exchange.fetch_order(
                    order_info['order']['id'], self.symbol
                )
                
                if order_status['status'] == 'closed':
                    self.logger.info(f"订单已成交:{price:.2f},类型:{order_info['type']}")
                    # 在相反位置放置反向订单
                    if order_info['type'] == 'buy':
                        # 买单成交了，在更高价格放卖单
                        new_price = price * (1 + self.grid_spacing / 100)
                        new_order = self.exchange.create_limit_sell_order(
                            self.symbol, self.order_amount, new_price
                        )
                        self.orders[new_price] = {'type': 'sell', 'order': new_order}
                    else:
                        # 卖单成交了，在更低价格放买单
                        new_price = price * (1 - self.grid_spacing / 100)
                        new_order = self.exchange.create_limit_buy_order(
                            self.symbol, self.order_amount, new_price
                        )
                        self.orders[new_price] = {'type': 'buy', 'order': new_order}
                    
                    # 删除已成交的订单
                    del self.orders[price]
            except Exception as e:
                self.logger.error(f"检查订单状态失败:{e}")
    
    def run(self):
        """运行网格策略"""
        self.logger.info("网格交易策略已启动")
        self.running = True
        self.create_grid_orders()

        while self.running:
            try:
                # 获取并显示当前价格
                current_price = self.get_current_price()
                self.logger.info(f"当前价格: {current_price:.6f}")

                # 每12次价格检查（约1分钟）检查一次订单状态
                self.price_check_counter += 1
                if self.price_check_counter >= 12:
                    self.check_and_rebalance()
                    self.price_check_counter = 0

                # 每5秒检查一次价格
                time.sleep(5)
            except KeyboardInterrupt:
                self.logger.info("网格交易停止")
                break
            except Exception as e:
                self.logger.error(f"运行错误:{e}")
                time.sleep(10)
    
    def stop(self):
        """停止策略"""
        self.running = False
        # 取消所有挂单
        try:
            for price, order_info in self.orders.items():
                self.exchange.cancel_order(order_info['order']['id'], self.symbol)
            self.orders.clear()
            self.logger.info("网格交易已停止，所有订单已取消")
        except Exception as e:
            self.logger.error(f"停止策略时出错:{e}")


class MovingAverageStrategy:
    """
    移动平均线交易策略类

    移动平均线策略是一种经典的趋势跟踪策略，通过比较短期和长期移动平均线的关系
    来判断市场趋势，并据此进行买入和卖出操作。

    策略原理：
    1. 计算短期移动平均线（如5日均线）和长期移动平均线（如20日均线）
    2. 当短期均线从下方穿越长期均线时，产生买入信号（金叉）
    3. 当短期均线从上方穿越长期均线时，产生卖出信号（死叉）
    4. 通过跟随趋势方向来获取收益

    适用场景：
    - 趋势明显的市场：有明确的上涨或下跌趋势
    - 流动性好的交易对：确保能够及时进出场
    - 中长期交易：适合持仓时间较长的交易风格

    风险提示：
    - 在震荡市场中可能产生频繁的假信号
    - 移动平均线具有滞后性，可能错过最佳进出场时机
    - 需要合理设置止损，避免趋势反转时的大幅亏损

    参数说明：
    - exchange: 交易所对象
    - symbol: 交易对，如 'CFX/USDT'
    - short_period: 短期均线周期，建议 5-20
    - long_period: 长期均线周期，建议 20-100
    - amount: 每次交易数量

    使用示例：
    ```python
    # 创建移动平均线策略
    strategy = MovingAverageStrategy(
        exchange=exchange,
        symbol='CFX/USDT',
        short_period=5,        # 5日短期均线
        long_period=20,        # 20日长期均线
        amount=100             # 每次交易100个代币
    )

    # 启动策略
    strategy.start()
    ```
    """
    def __init__(self, exchange, symbol, short_period=10, long_period=30, amount=100):
        self.logger = get_logger("moving_average")

        try:
            # 参数验证
            self._validate_parameters(symbol, short_period, long_period, amount)

            self.exchange = exchange
            self.symbol = symbol
            self.short_period = short_period
            self.long_period = long_period
            self.amount = amount
            self.position = 0  # 1表示持有多头，-1表示持有空头，0表示无仓
            self.last_action_time = 0
            self.running = False

            # 兼容旧参数名
            self.short_window = short_period
            self.long_window = long_period

            self.logger.info(f"移动平均线策略初始化成功: {symbol}, 短期: {short_period}, 长期: {long_period}, 数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "MovingAverageStrategy",
                "symbol": symbol,
                "short_period": short_period,
                "long_period": long_period,
                "amount": amount
            })
            self.logger.error(f"移动平均线策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"移动平均线策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, short_period, long_period, amount):
        """验证移动平均线参数"""
        validator.validate_symbol(symbol)
        validator.validate_ma_parameters(short_period, long_period, amount)
    
    def get_historical_data(self, timeframe='1h', limit=100):
        """获取历史价格数据"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(
                self.symbol, timeframe, limit=limit
            )
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            self.logger.error(f"获取历史数据失败:{e}")
            return None
    
    def calculate_moving_averages(self, df):
        """计算移动平均线"""
        df['ma_short'] = df['close'].rolling(window=self.short_window).mean()
        df['ma_long'] = df['close'].rolling(window=self.long_window).mean()
        return df
    
    def generate_signals(self, df):
        """生成交易信号"""
        df['signal'] = 0
        df['position'] = 0
        
        # 金叉信号(短期均线上穿长期均线)
        golden_cross = (df['ma_short'] > df['ma_long']) & \
                      (df['ma_short'].shift(1) <= df['ma_long'].shift(1))
        
        # 死叉信号(短期均线下穿长期均线)
        death_cross = (df['ma_short'] < df['ma_long']) & \
                     (df['ma_short'].shift(1) >= df['ma_long'].shift(1))
        
        df.loc[golden_cross, 'signal'] = 1  # 买入信号
        df.loc[death_cross, 'signal'] = -1  # 卖出信号

        return df

    def execute_trade(self, signal, current_price):
        """执行交易"""
        current_time = time.time()
        # 防止频繁交易，至少间隔1小时
        if current_time - self.last_action_time < 3600:
            return

        try:
            balance = self.exchange.fetch_balance()
            if signal == 1 and self.position <= 0:  # 买入信号
                # 用70%的USDT买入
                usdt_balance = balance['USDT']['free']
                buy_amount = (usdt_balance * 0.7) / current_price
                if buy_amount > 0.001:  # 最小交易数量
                    order = self.exchange.create_market_buy_order(
                        self.symbol, buy_amount
                    )
                    self.position = 1
                    self.last_action_time = current_time
                    log_trade("moving_average", "买入", self.symbol, buy_amount, current_price, order.get('id'))
                    self.logger.info(f"买入执行:{buy_amount:.6f} @ {current_price:.2f}")

            elif signal == -1 and self.position >= 0:  # 卖出信号
                # 卖出所有持仓
                base_currency = self.symbol.split('/')[0]
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(
                        self.symbol, sell_amount
                    )
                    self.position = -1
                    self.last_action_time = current_time
                    log_trade("moving_average", "卖出", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"卖出执行:{sell_amount:.6f} @ {current_price:.2f}")
        except Exception as e:
            log_error("moving_average", "交易执行失败", e)
            self.logger.error(f"交易执行失败:{e}")

    def run_backtest(self, days=30):
        """回测策略"""
        df = self.get_historical_data(timeframe='1h', limit=days*24)
        if df is None:
            return

        df = self.calculate_moving_averages(df)
        df = self.generate_signals(df)

        # 计算策略收益
        df['returns'] = df['close'].pct_change()
        df['strategy_returns'] = df['signal'].shift(1) * df['returns']

        # 计算累计收益
        df['cumulative_returns'] = (1 + df['returns']).cumprod()
        df['cumulative_strategy_returns'] = (1 + df['strategy_returns']).cumprod()

        # 打印回测结果
        total_return = df['cumulative_strategy_returns'].iloc[-1] - 1
        sharpe_ratio = df['strategy_returns'].mean() / df['strategy_returns'].std() * np.sqrt(24*365)

        self.logger.info(f"回测期间总收益:{total_return:.2%}")
        self.logger.info(f"夏普比率:{sharpe_ratio:.2f}")

        return df

    def run_live(self):
        """实时交易"""
        self.logger.info("开始移动平均线实时交易...")
        self.running = True

        while self.running:
            try:
                df = self.get_historical_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.calculate_moving_averages(df)
                df = self.generate_signals(df)

                # 获取最新信号
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]

                # 显示当前价格和均线状态
                short_ma = df['short_ma'].iloc[-1]
                long_ma = df['long_ma'].iloc[-1]
                trend = "上升" if short_ma > long_ma else "下降"
                self.logger.info(f"当前价格: {current_price:.6f}, 短期均线: {short_ma:.6f}, 长期均线: {long_ma:.6f}, 趋势: {trend}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price)

                # 每5分钟检查一次
                time.sleep(300)
            except KeyboardInterrupt:
                self.logger.info("策略停止")
                break
            except Exception as e:
                log_error("moving_average", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = getattr(self, 'last_price', price)
                self.last_price = price
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price
                return price

            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            return getattr(self, 'last_price', 0.0)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("moving_average")


class RSIStrategy:
    def __init__(self, exchange, symbol, period=14, oversold=30, overbought=70, amount=100):
        self.logger = get_logger("rsi_strategy")

        try:
            # 参数验证
            self._validate_parameters(symbol, period, oversold, overbought, amount)

            self.exchange = exchange
            self.symbol = symbol
            self.period = period
            self.oversold = oversold
            self.overbought = overbought
            self.amount = amount
            self.position = 0
            self.last_action_time = 0
            self.running = False

            # 兼容旧参数名
            self.rsi_period = period
            self.oversold_threshold = oversold
            self.overbought_threshold = overbought

            self.logger.info(f"RSI策略初始化成功: {symbol}, 周期: {period}, 超卖: {oversold}, 超买: {overbought}, 数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "RSIStrategy",
                "symbol": symbol,
                "period": period,
                "oversold": oversold,
                "overbought": overbought,
                "amount": amount
            })
            self.logger.error(f"RSI策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"RSI策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, period, oversold, overbought, amount):
        """验证RSI参数"""
        validator.validate_symbol(symbol)
        validator.validate_rsi_parameters(period, oversold, overbought, amount)

    def calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            # 兼容Gate.io适配器和CCXT接口
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            else:
                # 如果是Gate.io适配器
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            # 计算RSI
            df['rsi'] = self.calculate_rsi(df['close'], self.rsi_period)
            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败:{e}")
            return None

    def generate_signals(self, df):
        """生成交易信号"""
        df['signal'] = 0
        # RSI超卖信号(RSI < 30)
        oversold_condition = df['rsi'] < self.oversold_threshold
        df.loc[oversold_condition, 'signal'] = 1
        # RSI超买信号(RSI > 70)
        overbought_condition = df['rsi'] > self.overbought_threshold
        df.loc[overbought_condition, 'signal'] = -1
        return df

    def calculate_position_size(self, current_price, risk_per_trade=0.02):
        """计算仓位大小"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 风险控制:每次交易不超过账户余额2%
            position_value = account_balance * risk_per_trade
            position_size = position_value / current_price
            return max(position_size, 0.001)  # 最小数量
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            return 0.001

    def run_live(self):
        """实时运行RSI策略"""
        self.logger.info("开始RSI反转策略...")
        self.running = True

        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.generate_signals(df)
                # 获取最新数据
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]
                current_rsi = df['rsi'].iloc[-1]

                self.logger.info(f"当前价格: {current_price:.6f}, RSI: {current_rsi:.2f}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price, current_rsi)

                time.sleep(600)  # 10分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("RSI策略停止")
                break
            except Exception as e:
                log_error("rsi_strategy", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def get_current_price(self):
        """获取当前价格"""
        try:
            # 方法1: 使用ticker获取最新价格
            ticker = self.exchange.fetch_ticker(self.symbol)
            price = float(ticker['last'])

            if price > 0:
                self.last_price = getattr(self, 'last_price', price)
                self.last_price = price
                return price

            # 方法2: 如果ticker价格异常，使用orderbook
            orderbook = self.exchange.fetch_order_book(self.symbol, limit=5)
            if orderbook['bids'] and orderbook['asks']:
                bid_price = float(orderbook['bids'][0][0])
                ask_price = float(orderbook['asks'][0][0])
                price = (bid_price + ask_price) / 2
                self.last_price = price
                return price

            # 方法3: 使用最近交易记录
            trades = self.exchange.fetch_trades(self.symbol, limit=1)
            if trades:
                price = float(trades[0]['price'])
                self.last_price = price
                return price

            # 如果所有方法都失败，返回缓存价格
            return getattr(self, 'last_price', 0.0)

        except Exception as e:
            self.logger.error(f"获取价格失败: {e}")
            return getattr(self, 'last_price', 0.0)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("rsi_strategy")

    def execute_trade(self, signal, current_price, current_rsi):
        """执行交易"""
        current_time = time.time()
        # 防止频繁交易
        if current_time - self.last_action_time < 1800:  # 30分钟间隔
            return

        try:
            if signal == 1 and self.position <= 0:  # 买入信号
                position_size = self.calculate_position_size(current_price)
                order = self.exchange.create_market_buy_order(self.symbol, position_size)
                self.position = 1
                self.last_action_time = current_time
                log_trade("rsi_strategy", "买入", self.symbol, position_size, current_price, order.get('id'))
                self.logger.info(f"RSI超卖买入:{position_size:.6f} @ {current_price:.2f},RSI:{current_rsi:.2f}")

            elif signal == -1 and self.position >= 0:  # 卖出信号
                # 卖出所有持仓
                balance = self.exchange.fetch_balance()
                base_currency = self.symbol.split('/')[0]
                sell_amount = balance[base_currency]['free']
                if sell_amount > 0.001:
                    order = self.exchange.create_market_sell_order(self.symbol, sell_amount)
                    self.position = -1
                    self.last_action_time = current_time
                    log_trade("rsi_strategy", "卖出", self.symbol, sell_amount, current_price, order.get('id'))
                    self.logger.info(f"RSI超买卖出:{sell_amount:.6f} @ {current_price:.2f},RSI:{current_rsi:.2f}")
        except Exception as e:
            log_error("rsi_strategy", "交易执行失败", e)
            self.logger.error(f"交易执行失败:{e}")

    def run_backtest(self, days=60):
        """回测RSI策略"""
        df = self.get_market_data(timeframe='4h', limit=days*6)
        if df is None:
            return

        df = self.generate_signals(df)
        # 模拟交易
        capital = 10000  # 初始资金
        position = 0
        trades = []

        for i in range(len(df)):
            if pd.isna(df['rsi'].iloc[i]):
                continue

            current_price = df['close'].iloc[i]
            current_signal = df['signal'].iloc[i]
            current_rsi = df['rsi'].iloc[i]

            if current_signal == 1 and position <= 0:  # 买入
                position = capital / current_price
                capital = 0
                trades.append({
                    'type': 'buy',
                    'price': current_price,
                    'rsi': current_rsi,
                    'time': df['timestamp'].iloc[i]
                })
            elif current_signal == -1 and position > 0:  # 卖出
                capital = position * current_price
                position = 0
                trades.append({
                    'type': 'sell',
                    'price': current_price,
                    'rsi': current_rsi,
                    'time': df['timestamp'].iloc[i]
                })

        # 计算最终收益
        final_value = capital + position * df['close'].iloc[-1]
        total_return = (final_value - 10000) / 10000

        self.logger.info(f"RSI策略回测结果:")
        self.logger.info(f"初始资金:$10,000")
        self.logger.info(f"最终价值:${final_value:.2f}")
        self.logger.info(f"总收益率:{total_return:.2%}")
        self.logger.info(f"交易次数:{len(trades)}")

        return trades

    def run_live(self):
        """实时运行RSI策略"""
        self.logger.info("开始RSI反转策略...")
        self.running = True

        while self.running:
            try:
                df = self.get_market_data(timeframe='1h', limit=50)
                if df is None:
                    time.sleep(300)
                    continue

                df = self.generate_signals(df)
                # 获取最新数据
                latest_signal = df['signal'].iloc[-1]
                current_price = df['close'].iloc[-1]
                current_rsi = df['rsi'].iloc[-1]

                self.logger.info(f"当前价格: {current_price:.6f}, RSI: {current_rsi:.2f}")

                if latest_signal != 0:
                    self.execute_trade(latest_signal, current_price, current_rsi)

                time.sleep(600)  # 10分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("RSI策略停止")
                break
            except Exception as e:
                log_error("rsi_strategy", "运行错误", e)
                self.logger.error(f"运行错误: {e}")
                time.sleep(60)

    def stop(self):
        """停止策略"""
        self.running = False
        log_strategy_stop("rsi_strategy")


class VolumeBreakoutStrategy:
    def __init__(self, exchange, symbol, lookback_period=20, volume_multiplier=2, breakout_threshold=0.02, amount=100):
        self.logger = get_logger("volume_breakout")

        try:
            # 参数验证
            self._validate_parameters(symbol, lookback_period, volume_multiplier, breakout_threshold, amount)

            self.exchange = exchange
            self.symbol = symbol
            self.lookback_period = lookback_period
            self.volume_multiplier = volume_multiplier
            self.breakout_threshold = breakout_threshold
            self.amount = amount
            self.position = 0
            self.entry_price = 0
            self.stop_loss_pct = 0.03  # 3%止损
            self.take_profit_pct = 0.06  # 6%止盈
            self.running = False

            self.logger.info(f"成交量突破策略初始化成功: {symbol}, 回看周期: {lookback_period}, 成交量倍数: {volume_multiplier}, 突破阈值: {breakout_threshold}%, 数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "VolumeBreakoutStrategy",
                "symbol": symbol,
                "lookback_period": lookback_period,
                "volume_multiplier": volume_multiplier,
                "breakout_threshold": breakout_threshold,
                "amount": amount
            })
            self.logger.error(f"成交量突破策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"成交量突破策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, lookback_period, volume_multiplier, breakout_threshold, amount):
        """验证成交量突破参数"""
        validator.validate_symbol(symbol)
        validator.validate_integer(lookback_period, min_value=5, max_value=100)
        validator.validate_amount(volume_multiplier)
        validator.validate_percentage(breakout_threshold)
        validator.validate_amount(amount)

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            # 兼容Gate.io适配器和CCXT接口
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
            else:
                # 如果是Gate.io适配器
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit)

            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # 计算技术指标
            df['volume_ma'] = df['volume'].rolling(window=self.lookback_period).mean()
            df['price_ma'] = df['close'].rolling(window=self.lookback_period).mean()
            df['resistance'] = df['high'].rolling(window=self.lookback_period).max()
            df['support'] = df['low'].rolling(window=self.lookback_period).min()

            return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def detect_breakout(self, df):
        """检测突破信号"""
        df['breakout_signal'] = 0

        for i in range(self.lookback_period, len(df)):
            current_row = df.iloc[i]
            prev_row = df.iloc[i-1]

            # 向上突破条件
            upward_breakout = (
                current_row['close'] > prev_row['resistance'] * (1 + self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] > current_row['open']  # 阳线
            )

            # 向下突破条件
            downward_breakout = (
                current_row['close'] < prev_row['support'] * (1 - self.breakout_threshold) and
                current_row['volume'] > current_row['volume_ma'] * self.volume_multiplier and
                current_row['close'] < current_row['open']  # 阴线
            )

            if upward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = 1
            elif downward_breakout:
                df.iloc[i, df.columns.get_loc('breakout_signal')] = -1

        return df

    def calculate_position_size(self, current_price, account_risk=0.02):
        """根据风险控制计算仓位"""
        try:
            balance = self.exchange.fetch_balance()
            account_balance = balance['USDT']['free']
            # 根据止损比例计算仓位
            risk_amount = account_balance * account_risk
            position_value = risk_amount / self.stop_loss_pct
            position_size = position_value / current_price
            return max(position_size, 0.001)
        except Exception as e:
            self.logger.error(f"计算仓位失败:{e}")
            return 0.001

    def stop(self):
        """停止成交量突破策略"""
        self.running = False
        self.logger.info("成交量突破策略已停止")
        log_strategy_stop("volume_breakout_strategy")


class AwesomeOscillatorStrategy:
    """
    AO指标策略类 (Awesome Oscillator Strategy)

    AO指标是由Bill Williams开发的动量振荡器，通过比较短期和长期简单移动平均线
    来识别市场动量的变化。AO指标具有超前性，能够比MACD等传统指标更早地
    发现趋势反转信号。

    核心原理：
    1. 计算公式：AO = SMA(中价, 5) - SMA(中价, 34)
    2. 中价 = (最高价 + 最低价) / 2
    3. 5日均线代表短期动能，34日均线代表长期动能
    4. 差值反映动量的加速或减速

    信号类型：
    1. 碟型信号：零轴上方绿柱后接红柱再转绿→买入信号
    2. 穿越零轴：柱状图由负转正→金叉买入，由正转负→死叉卖出
    3. 双峰信号：零轴下方第二个低点高于第一个→底部反转
    4. 顶背离：价格新高但AO柱高度下降→趋势衰竭预警
    5. 底背离：价格新低但AO柱高度上升→底部反转预警

    超前性优势：
    - 比MACD平均提前1-2根K线发出信号
    - 基于价格中值而非收盘价，对波动更敏感
    - 能够在趋势反转初期提供预警

    适用场景：
    - 趋势市场的早期识别
    - 震荡市场的买卖点捕捉
    - 与其他指标配合使用提高准确率

    风险提示：
    - 在强趋势市场中可能产生假信号
    - 需要结合价格行为和其他指标确认
    - 建议设置止损保护
    """

    def __init__(self, exchange, symbol, short_period=5, long_period=34, amount=100,
                 stop_loss_pct=0.03, take_profit_pct=0.06):
        """
        初始化AO指标策略

        Args:
            exchange: 交易所对象
            symbol (str): 交易对符号，如 'CFX/USDT'
            short_period (int): 短期均线周期，默认5
            long_period (int): 长期均线周期，默认34
            amount (float): 交易数量
            stop_loss_pct (float): 止损百分比，默认3%
            take_profit_pct (float): 止盈百分比，默认6%
        """
        self.logger = get_logger("awesome_oscillator")

        try:
            # 参数验证
            self._validate_parameters(symbol, short_period, long_period, amount,
                                    stop_loss_pct, take_profit_pct)

            self.exchange = exchange
            self.symbol = symbol
            self.short_period = short_period
            self.long_period = long_period
            self.amount = amount
            self.stop_loss_pct = stop_loss_pct
            self.take_profit_pct = take_profit_pct

            # 策略状态
            self.position = 0  # 0: 无仓位, 1: 多头, -1: 空头
            self.entry_price = 0
            self.running = False

            # AO指标历史数据
            self.ao_history = []
            self.price_history = []

            # 信号状态
            self.last_ao_value = 0
            self.last_ao_color = None  # 'green' or 'red'
            self.zero_cross_signal = None

            self.logger.info(f"AO指标策略初始化成功: {symbol}, 短期周期: {short_period}, "
                           f"长期周期: {long_period}, 交易数量: {amount}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "AwesomeOscillatorStrategy",
                "symbol": symbol,
                "short_period": short_period,
                "long_period": long_period,
                "amount": amount
            })
            self.logger.error(f"AO策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"AO策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, short_period, long_period, amount,
                           stop_loss_pct, take_profit_pct):
        """验证AO策略参数"""
        validator.validate_symbol(symbol)
        validator.validate_integer(short_period, min_value=1, max_value=50)
        validator.validate_integer(long_period, min_value=10, max_value=200)
        validator.validate_amount(amount)
        validator.validate_percentage(stop_loss_pct)
        validator.validate_percentage(take_profit_pct)

        if short_period >= long_period:
            raise ValidationError("短期周期必须小于长期周期")

    def get_market_data(self, timeframe='1h', limit=100):
        """获取市场数据"""
        try:
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                return df
            else:
                # 模拟数据
                dates = pd.date_range(start='2024-01-01', periods=limit, freq='1H')
                np.random.seed(42)
                base_price = 0.21375
                prices = []
                for i in range(limit):
                    change = np.random.normal(0, 0.02)
                    base_price *= (1 + change)
                    prices.append(base_price)

                df = pd.DataFrame({
                    'timestamp': dates,
                    'open': prices,
                    'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                    'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                    'close': prices,
                    'volume': np.random.randint(1000, 10000, limit)
                })
                return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def calculate_awesome_oscillator(self, df):
        """
        计算AO指标 (Awesome Oscillator)

        公式：AO = SMA(中价, 5) - SMA(中价, 34)
        中价 = (最高价 + 最低价) / 2

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            DataFrame: 添加了AO指标相关列的数据
        """
        try:
            # 计算中价 (Median Price)
            df['median_price'] = (df['high'] + df['low']) / 2

            # 计算短期和长期简单移动平均线
            df['sma_short'] = df['median_price'].rolling(window=self.short_period).mean()
            df['sma_long'] = df['median_price'].rolling(window=self.long_period).mean()

            # 计算AO指标
            df['ao'] = df['sma_short'] - df['sma_long']

            # 计算AO柱状图颜色（绿色=1，红色=-1）
            df['ao_color'] = 0
            for i in range(1, len(df)):
                if df.iloc[i]['ao'] > df.iloc[i-1]['ao']:
                    df.iloc[i, df.columns.get_loc('ao_color')] = 1  # 绿色（动能增强）
                elif df.iloc[i]['ao'] > df.iloc[i-1]['ao']:
                    df.iloc[i, df.columns.get_loc('ao_color')] = -1  # 红色（动能减弱）

            # 计算零轴穿越信号
            df['zero_cross'] = 0
            for i in range(1, len(df)):
                prev_ao = df.iloc[i-1]['ao']
                curr_ao = df.iloc[i]['ao']

                # 金叉：从负值穿越到正值
                if prev_ao <= 0 and curr_ao > 0:
                    df.iloc[i, df.columns.get_loc('zero_cross')] = 1
                # 死叉：从正值穿越到负值
                elif prev_ao >= 0 and curr_ao < 0:
                    df.iloc[i, df.columns.get_loc('zero_cross')] = -1

            return df

        except Exception as e:
            self.logger.error(f"计算AO指标失败: {e}")
            return df

    def detect_saucer_signal(self, df, lookback=5):
        """
        检测碟型信号 (Saucer Signal)

        碟型信号：零轴上方绿柱后接红柱再转绿→买入信号

        Args:
            df: 包含AO数据的DataFrame
            lookback: 回看周期

        Returns:
            Series: 碟型信号标记
        """
        signals = pd.Series(0, index=df.index)

        try:
            for i in range(lookback, len(df)):
                # 检查当前是否在零轴上方
                if df.iloc[i]['ao'] > 0:
                    # 检查最近几根K线的颜色模式
                    recent_colors = df.iloc[i-lookback+1:i+1]['ao_color'].tolist()

                    # 寻找绿-红-绿的模式
                    for j in range(len(recent_colors) - 2):
                        if (recent_colors[j] == 1 and      # 绿色
                            recent_colors[j+1] == -1 and   # 红色
                            recent_colors[j+2] == 1):      # 绿色
                            signals.iloc[i] = 1
                            break

            return signals

        except Exception as e:
            self.logger.error(f"检测碟型信号失败: {e}")
            return signals

    def detect_twin_peaks_signal(self, df, lookback=20):
        """
        检测双峰信号 (Twin Peaks Signal)

        双峰信号：零轴下方第二个低点高于第一个→底部反转

        Args:
            df: 包含AO数据的DataFrame
            lookback: 回看周期

        Returns:
            Series: 双峰信号标记
        """
        signals = pd.Series(0, index=df.index)

        try:
            for i in range(lookback, len(df)):
                # 检查当前是否在零轴下方
                if df.iloc[i]['ao'] < 0:
                    # 在回看期内寻找两个低点
                    recent_ao = df.iloc[i-lookback:i+1]['ao']

                    # 找到局部最小值
                    local_mins = []
                    for j in range(1, len(recent_ao) - 1):
                        if (recent_ao.iloc[j] < recent_ao.iloc[j-1] and
                            recent_ao.iloc[j] < recent_ao.iloc[j+1]):
                            local_mins.append((j, recent_ao.iloc[j]))

                    # 如果找到至少两个低点，检查第二个是否高于第一个
                    if len(local_mins) >= 2:
                        first_low = local_mins[-2][1]  # 第一个低点
                        second_low = local_mins[-1][1]  # 第二个低点

                        if second_low > first_low:
                            signals.iloc[i] = 1

            return signals

        except Exception as e:
            self.logger.error(f"检测双峰信号失败: {e}")
            return signals

    def detect_divergence(self, df, lookback=20):
        """
        检测背离信号

        顶背离：价格新高但AO柱高度下降→趋势衰竭预警
        底背离：价格新低但AO柱高度上升→底部反转预警

        Args:
            df: 包含价格和AO数据的DataFrame
            lookback: 回看周期

        Returns:
            tuple: (顶背离信号, 底背离信号)
        """
        bullish_div = pd.Series(0, index=df.index)  # 底背离（看涨）
        bearish_div = pd.Series(0, index=df.index)  # 顶背离（看跌）

        try:
            for i in range(lookback, len(df)):
                recent_data = df.iloc[i-lookback:i+1]

                # 找到价格和AO的极值点
                price_highs = []
                price_lows = []
                ao_highs = []
                ao_lows = []

                for j in range(1, len(recent_data) - 1):
                    # 价格高点
                    if (recent_data.iloc[j]['high'] > recent_data.iloc[j-1]['high'] and
                        recent_data.iloc[j]['high'] > recent_data.iloc[j+1]['high']):
                        price_highs.append((j, recent_data.iloc[j]['high']))
                        ao_highs.append((j, recent_data.iloc[j]['ao']))

                    # 价格低点
                    if (recent_data.iloc[j]['low'] < recent_data.iloc[j-1]['low'] and
                        recent_data.iloc[j]['low'] < recent_data.iloc[j+1]['low']):
                        price_lows.append((j, recent_data.iloc[j]['low']))
                        ao_lows.append((j, recent_data.iloc[j]['ao']))

                # 检测顶背离
                if len(price_highs) >= 2 and len(ao_highs) >= 2:
                    latest_price_high = price_highs[-1][1]
                    prev_price_high = price_highs[-2][1]
                    latest_ao_high = ao_highs[-1][1]
                    prev_ao_high = ao_highs[-2][1]

                    if latest_price_high > prev_price_high and latest_ao_high < prev_ao_high:
                        bearish_div.iloc[i] = 1

                # 检测底背离
                if len(price_lows) >= 2 and len(ao_lows) >= 2:
                    latest_price_low = price_lows[-1][1]
                    prev_price_low = price_lows[-2][1]
                    latest_ao_low = ao_lows[-1][1]
                    prev_ao_low = ao_lows[-2][1]

                    if latest_price_low < prev_price_low and latest_ao_low > prev_ao_low:
                        bullish_div.iloc[i] = 1

            return bullish_div, bearish_div

        except Exception as e:
            self.logger.error(f"检测背离信号失败: {e}")
            return bullish_div, bearish_div

    def generate_signals(self, df):
        """
        生成综合交易信号

        结合多种AO信号类型生成最终的买卖信号

        Args:
            df: 包含所有指标的DataFrame

        Returns:
            DataFrame: 添加了信号列的数据
        """
        try:
            # 计算AO指标
            df = self.calculate_awesome_oscillator(df)

            # 检测各种信号
            saucer_signals = self.detect_saucer_signal(df)
            twin_peaks_signals = self.detect_twin_peaks_signal(df)
            bullish_div, bearish_div = self.detect_divergence(df)

            # 初始化信号列
            df['ao_signal'] = 0
            df['signal_type'] = ''
            df['signal_strength'] = 0

            for i in range(len(df)):
                signals = []
                signal_strength = 0

                # 零轴穿越信号（权重：2）
                if df.iloc[i]['zero_cross'] == 1:
                    signals.append('金叉')
                    signal_strength += 2
                elif df.iloc[i]['zero_cross'] == -1:
                    signals.append('死叉')
                    signal_strength -= 2

                # 碟型信号（权重：3）
                if saucer_signals.iloc[i] == 1:
                    signals.append('碟型')
                    signal_strength += 3

                # 双峰信号（权重：3）
                if twin_peaks_signals.iloc[i] == 1:
                    signals.append('双峰')
                    signal_strength += 3

                # 背离信号（权重：2）
                if bullish_div.iloc[i] == 1:
                    signals.append('底背离')
                    signal_strength += 2
                elif bearish_div.iloc[i] == 1:
                    signals.append('顶背离')
                    signal_strength -= 2

                # 设置最终信号
                if signal_strength >= 3:
                    df.iloc[i, df.columns.get_loc('ao_signal')] = 1  # 买入
                elif signal_strength <= -2:
                    df.iloc[i, df.columns.get_loc('ao_signal')] = -1  # 卖出

                df.iloc[i, df.columns.get_loc('signal_type')] = '+'.join(signals)
                df.iloc[i, df.columns.get_loc('signal_strength')] = signal_strength

            return df

        except Exception as e:
            self.logger.error(f"生成交易信号失败: {e}")
            return df

    def execute_trade(self, signal, current_price, timestamp):
        """
        执行交易

        Args:
            signal: 交易信号 (1: 买入, -1: 卖出, 0: 无操作)
            current_price: 当前价格
            timestamp: 时间戳
        """
        try:
            if signal == 1 and self.position <= 0:
                # 买入信号
                if self.position == -1:
                    # 先平空头仓位
                    self._close_position(current_price, timestamp, "平空")

                # 开多头仓位
                order = self.exchange.create_limit_buy_order(
                    self.symbol, self.amount, current_price
                )

                self.position = 1
                self.entry_price = current_price

                log_trade(self.symbol, "买入", self.amount, current_price, timestamp)
                self.logger.info(f"AO策略买入: {self.amount} @ {current_price}")

            elif signal == -1 and self.position >= 0:
                # 卖出信号
                if self.position == 1:
                    # 先平多头仓位
                    self._close_position(current_price, timestamp, "平多")

                # 开空头仓位
                order = self.exchange.create_limit_sell_order(
                    self.symbol, self.amount, current_price
                )

                self.position = -1
                self.entry_price = current_price

                log_trade(self.symbol, "卖出", self.amount, current_price, timestamp)
                self.logger.info(f"AO策略卖出: {self.amount} @ {current_price}")

        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")

    def _close_position(self, current_price, timestamp, action):
        """平仓操作"""
        try:
            if self.position == 1:
                # 平多头仓位
                order = self.exchange.create_limit_sell_order(
                    self.symbol, self.amount, current_price
                )
                pnl = (current_price - self.entry_price) / self.entry_price * 100

            elif self.position == -1:
                # 平空头仓位
                order = self.exchange.create_limit_buy_order(
                    self.symbol, self.amount, current_price
                )
                pnl = (self.entry_price - current_price) / self.entry_price * 100

            log_trade(self.symbol, action, self.amount, current_price, timestamp, pnl)
            self.logger.info(f"AO策略{action}: {self.amount} @ {current_price}, 盈亏: {pnl:.2f}%")

            self.position = 0
            self.entry_price = 0

        except Exception as e:
            self.logger.error(f"平仓操作失败: {e}")

    def check_risk_management(self, current_price):
        """检查风险管理"""
        try:
            if self.position != 0 and self.entry_price > 0:
                if self.position == 1:
                    # 多头仓位
                    pnl_pct = (current_price - self.entry_price) / self.entry_price

                    # 止损
                    if pnl_pct <= -self.stop_loss_pct:
                        self._close_position(current_price, datetime.now(), "止损")
                        return True

                    # 止盈
                    if pnl_pct >= self.take_profit_pct:
                        self._close_position(current_price, datetime.now(), "止盈")
                        return True

                elif self.position == -1:
                    # 空头仓位
                    pnl_pct = (self.entry_price - current_price) / self.entry_price

                    # 止损
                    if pnl_pct <= -self.stop_loss_pct:
                        self._close_position(current_price, datetime.now(), "止损")
                        return True

                    # 止盈
                    if pnl_pct >= self.take_profit_pct:
                        self._close_position(current_price, datetime.now(), "止盈")
                        return True

            return False

        except Exception as e:
            self.logger.error(f"风险管理检查失败: {e}")
            return False

    def run_live(self, timeframe='1h'):
        """
        实时运行AO策略

        Args:
            timeframe: K线周期，默认1小时
        """
        self.running = True
        log_strategy_start("awesome_oscillator_strategy")
        self.logger.info(f"AO策略开始运行: {self.symbol}, 周期: {timeframe}")

        try:
            while self.running:
                # 获取最新市场数据
                df = self.get_market_data(timeframe, limit=max(100, self.long_period + 20))

                if df is None or len(df) < self.long_period:
                    self.logger.warning("市场数据不足，等待下次检查")
                    time.sleep(60)
                    continue

                # 生成交易信号
                df = self.generate_signals(df)

                # 获取最新信号
                latest_signal = df.iloc[-1]['ao_signal']
                latest_price = df.iloc[-1]['close']
                latest_timestamp = df.iloc[-1]['timestamp']
                signal_type = df.iloc[-1]['signal_type']
                signal_strength = df.iloc[-1]['signal_strength']

                # 记录信号信息
                if latest_signal != 0:
                    self.logger.info(f"AO信号: {latest_signal}, 类型: {signal_type}, "
                                   f"强度: {signal_strength}, 价格: {latest_price}")

                # 风险管理检查
                risk_triggered = self.check_risk_management(latest_price)

                # 执行交易（如果没有触发风险管理）
                if not risk_triggered:
                    self.execute_trade(latest_signal, latest_price, latest_timestamp)

                # 更新历史数据
                self.ao_history.append(df.iloc[-1]['ao'])
                self.price_history.append(latest_price)

                # 保持历史数据长度
                if len(self.ao_history) > 200:
                    self.ao_history = self.ao_history[-200:]
                    self.price_history = self.price_history[-200:]

                # 等待下次检查
                time.sleep(300)  # 5分钟检查一次

        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_EXECUTION_FAILED)
            self.logger.error(f"AO策略运行失败: {error_details['message']}")
            self.running = False

    def run_backtest(self, start_date, end_date, timeframe='1h'):
        """
        回测AO策略

        Args:
            start_date: 开始日期
            end_date: 结束日期
            timeframe: K线周期

        Returns:
            dict: 回测结果
        """
        try:
            self.logger.info(f"开始AO策略回测: {start_date} 到 {end_date}")

            # 获取历史数据
            df = self.get_market_data(timeframe, limit=1000)

            if df is None or len(df) < self.long_period:
                self.logger.error("历史数据不足，无法进行回测")
                return None

            # 生成信号
            df = self.generate_signals(df)

            # 回测统计
            trades = []
            total_return = 0
            win_trades = 0
            lose_trades = 0
            max_drawdown = 0
            peak_value = 0

            position = 0
            entry_price = 0

            for i in range(self.long_period, len(df)):
                current_price = df.iloc[i]['close']
                signal = df.iloc[i]['ao_signal']
                timestamp = df.iloc[i]['timestamp']

                # 执行交易逻辑
                if signal == 1 and position <= 0:
                    if position == -1:
                        # 平空头
                        pnl = (entry_price - current_price) / entry_price
                        total_return += pnl
                        trades.append({
                            'type': '平空',
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'pnl': pnl,
                            'timestamp': timestamp
                        })
                        if pnl > 0:
                            win_trades += 1
                        else:
                            lose_trades += 1

                    # 开多头
                    position = 1
                    entry_price = current_price

                elif signal == -1 and position >= 0:
                    if position == 1:
                        # 平多头
                        pnl = (current_price - entry_price) / entry_price
                        total_return += pnl
                        trades.append({
                            'type': '平多',
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'pnl': pnl,
                            'timestamp': timestamp
                        })
                        if pnl > 0:
                            win_trades += 1
                        else:
                            lose_trades += 1

                    # 开空头
                    position = -1
                    entry_price = current_price

                # 计算最大回撤
                current_value = 1 + total_return
                if current_value > peak_value:
                    peak_value = current_value
                else:
                    drawdown = (peak_value - current_value) / peak_value
                    max_drawdown = max(max_drawdown, drawdown)

            # 计算回测结果
            total_trades = win_trades + lose_trades
            win_rate = win_trades / total_trades if total_trades > 0 else 0

            result = {
                'total_return': total_return * 100,  # 转换为百分比
                'total_trades': total_trades,
                'win_trades': win_trades,
                'lose_trades': lose_trades,
                'win_rate': win_rate * 100,
                'max_drawdown': max_drawdown * 100,
                'trades': trades
            }

            self.logger.info(f"AO策略回测完成: 总收益 {result['total_return']:.2f}%, "
                           f"胜率 {result['win_rate']:.2f}%, 最大回撤 {result['max_drawdown']:.2f}%")

            return result

        except Exception as e:
            self.logger.error(f"AO策略回测失败: {e}")
            return None

    def get_strategy_status(self):
        """获取策略状态"""
        return {
            'running': self.running,
            'symbol': self.symbol,
            'position': self.position,
            'entry_price': self.entry_price,
            'short_period': self.short_period,
            'long_period': self.long_period,
            'stop_loss_pct': self.stop_loss_pct,
            'take_profit_pct': self.take_profit_pct,
            'ao_history_length': len(self.ao_history),
            'latest_ao': self.ao_history[-1] if self.ao_history else 0
        }

    def stop(self):
        """停止AO策略"""
        self.running = False
        self.logger.info("AO指标策略已停止")
        log_strategy_stop("awesome_oscillator_strategy")


class FMACDStrategy:
    """
    FMACD策略类 (Forward-looking MACD Strategy)

    改进型MACD策略，通过机器学习技术消除传统MACD的滞后问题。

    核心技术：
    1. 全连接神经网络（4隐藏层+ReLU激活）分析多维因子
    2. 预测次日收盘价并与历史数据融合，重构价格序列
    3. 基于重构数据计算FMACD（FDIF、FDEA），形成超前信号

    技术优势：
    - 比传统MACD提前1-3天发出信号
    - 年化收益提升约15%
    - 结合高频、财务、技术多维因子
    - 动态适应市场变化

    因子体系：
    1. 技术因子：价格动量、波动率、成交量指标
    2. 高频因子：分钟级价格变化、订单流指标
    3. 财务因子：基本面数据、市场情绪指标
    4. 宏观因子：市场环境、相关性指标

    神经网络架构：
    - 输入层：多维因子特征
    - 隐藏层：4层全连接层，每层128-256神经元
    - 激活函数：ReLU
    - 输出层：预测次日收盘价
    - 正则化：Dropout防止过拟合

    适用场景：
    - 中短期趋势交易
    - 高频量化策略
    - 多因子模型组合
    - 机器学习驱动的交易系统

    风险提示：
    - 模型需要定期重训练
    - 对数据质量要求较高
    - 市场环境变化可能影响预测效果
    """

    def __init__(self, exchange, symbol, fast_period=12, slow_period=26, signal_period=9,
                 amount=100, stop_loss_pct=0.03, take_profit_pct=0.06,
                 model_retrain_days=30, prediction_confidence=0.7):
        """
        初始化FMACD策略

        Args:
            exchange: 交易所对象
            symbol (str): 交易对符号，如 'CFX/USDT'
            fast_period (int): 快速EMA周期，默认12
            slow_period (int): 慢速EMA周期，默认26
            signal_period (int): 信号线EMA周期，默认9
            amount (float): 交易数量
            stop_loss_pct (float): 止损百分比，默认3%
            take_profit_pct (float): 止盈百分比，默认6%
            model_retrain_days (int): 模型重训练间隔天数，默认30天
            prediction_confidence (float): 预测置信度阈值，默认0.7
        """
        self.logger = get_logger("fmacd_strategy")

        try:
            # 参数验证
            self._validate_parameters(symbol, fast_period, slow_period, signal_period,
                                    amount, stop_loss_pct, take_profit_pct,
                                    model_retrain_days, prediction_confidence)

            self.exchange = exchange
            self.symbol = symbol
            self.fast_period = fast_period
            self.slow_period = slow_period
            self.signal_period = signal_period
            self.amount = amount
            self.stop_loss_pct = stop_loss_pct
            self.take_profit_pct = take_profit_pct
            self.model_retrain_days = model_retrain_days
            self.prediction_confidence = prediction_confidence

            # 策略状态
            self.position = 0  # 0: 无仓位, 1: 多头, -1: 空头
            self.entry_price = 0
            self.running = False

            # 机器学习模型相关
            self.ml_model = None
            self.feature_scaler = None
            self.last_retrain_date = None
            self.prediction_history = []

            # FMACD指标历史数据
            self.fmacd_history = []
            self.fdif_history = []
            self.fdea_history = []
            self.price_history = []
            self.feature_history = []

            # 初始化机器学习组件
            self._initialize_ml_components()

            self.logger.info(f"FMACD策略初始化成功: {symbol}, 快速周期: {fast_period}, "
                           f"慢速周期: {slow_period}, 信号周期: {signal_period}")

        except ValidationError as e:
            error_details = error_handler.handle_error(e, context={
                "strategy": "FMACDStrategy",
                "symbol": symbol,
                "fast_period": fast_period,
                "slow_period": slow_period,
                "signal_period": signal_period
            })
            self.logger.error(f"FMACD策略参数验证失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))
        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_INIT_FAILED)
            self.logger.error(f"FMACD策略初始化失败: {error_details['message']}")
            raise Exception(error_handler.format_user_message(error_details))

    def _validate_parameters(self, symbol, fast_period, slow_period, signal_period,
                           amount, stop_loss_pct, take_profit_pct,
                           model_retrain_days, prediction_confidence):
        """验证FMACD策略参数"""
        validator.validate_symbol(symbol)
        validator.validate_integer(fast_period, min_value=5, max_value=50)
        validator.validate_integer(slow_period, min_value=10, max_value=100)
        validator.validate_integer(signal_period, min_value=3, max_value=30)
        validator.validate_amount(amount)
        validator.validate_percentage(stop_loss_pct)
        validator.validate_percentage(take_profit_pct)
        validator.validate_integer(model_retrain_days, min_value=7, max_value=90)

        if fast_period >= slow_period:
            raise ValidationError("快速周期必须小于慢速周期")

        if not (0.5 <= prediction_confidence <= 0.95):
            raise ValidationError("预测置信度必须在0.5-0.95之间")

    def _initialize_ml_components(self):
        """初始化机器学习组件"""
        try:
            # 尝试导入机器学习库
            try:
                from sklearn.preprocessing import StandardScaler
                from sklearn.neural_network import MLPRegressor
                from sklearn.model_selection import train_test_split
                from sklearn.metrics import mean_squared_error

                self.sklearn_available = True
            except ImportError:
                self.sklearn_available = False
                self.logger.warning("scikit-learn库未安装，将使用简化版本")

            if self.sklearn_available:
                # 初始化特征标准化器
                from sklearn.preprocessing import StandardScaler
                from sklearn.neural_network import MLPRegressor

                self.feature_scaler = StandardScaler()

                # 初始化神经网络模型
                self.ml_model = MLPRegressor(
                    hidden_layer_sizes=(256, 128, 64, 32),  # 4隐藏层
                    activation='relu',                       # ReLU激活函数
                    solver='adam',                          # Adam优化器
                    alpha=0.001,                           # L2正则化
                    batch_size='auto',
                    learning_rate='adaptive',
                    max_iter=500,
                    random_state=42,
                    early_stopping=True,
                    validation_fraction=0.1,
                    n_iter_no_change=10
                )

                self.logger.info("机器学习组件初始化成功")
            else:
                # 使用简化的预测模型
                self.ml_model = None
                self.feature_scaler = None
                self.logger.info("使用简化预测模型")

        except Exception as e:
            self.logger.warning(f"机器学习组件初始化失败，将使用简化版本: {e}")
            # 使用简化的预测模型
            self.ml_model = None
            self.feature_scaler = None
            self.sklearn_available = False

    def get_market_data(self, timeframe='1h', limit=200):
        """获取市场数据"""
        try:
            if hasattr(self.exchange, 'fetch_ohlcv'):
                ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, limit=limit)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                return df
            else:
                # 模拟数据
                dates = pd.date_range(start='2024-01-01', periods=limit, freq='1H')
                np.random.seed(42)
                base_price = 0.21375
                prices = []
                volumes = []

                for i in range(limit):
                    # 添加趋势和随机波动
                    trend = 0.0001 * np.sin(i * 0.05)  # 长期趋势
                    momentum = 0.001 * np.sin(i * 0.2)  # 中期动量
                    noise = np.random.normal(0, 0.01)   # 随机噪音

                    base_price *= (1 + trend + momentum + noise)
                    prices.append(base_price)
                    volumes.append(np.random.randint(1000, 10000))

                df = pd.DataFrame({
                    'timestamp': dates,
                    'open': prices,
                    'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
                    'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
                    'close': prices,
                    'volume': volumes
                })
                return df
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None

    def extract_features(self, df):
        """
        提取多维因子特征

        包含技术因子、高频因子、财务因子、宏观因子

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            DataFrame: 添加了特征列的数据
        """
        try:
            # 技术因子
            # 1. 价格动量指标
            df['returns'] = df['close'].pct_change()
            df['returns_5'] = df['close'].pct_change(5)
            df['returns_10'] = df['close'].pct_change(10)
            df['returns_20'] = df['close'].pct_change(20)

            # 2. 移动平均线
            df['sma_5'] = df['close'].rolling(5).mean()
            df['sma_10'] = df['close'].rolling(10).mean()
            df['sma_20'] = df['close'].rolling(20).mean()
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()

            # 3. 波动率指标
            df['volatility_5'] = df['returns'].rolling(5).std()
            df['volatility_10'] = df['returns'].rolling(10).std()
            df['volatility_20'] = df['returns'].rolling(20).std()

            # 4. 价格位置指标
            df['high_low_ratio'] = df['high'] / df['low']
            df['close_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])

            # 高频因子
            # 1. 分钟级价格变化
            df['price_acceleration'] = df['returns'].diff()
            df['price_momentum'] = df['returns'].rolling(3).mean()

            # 2. 成交量指标
            df['volume_sma'] = df['volume'].rolling(10).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            df['price_volume'] = df['close'] * df['volume']
            df['vwap'] = df['price_volume'].rolling(10).sum() / df['volume'].rolling(10).sum()

            # 3. 订单流指标（模拟）
            df['buy_pressure'] = np.where(df['close'] > df['open'], df['volume'], 0)
            df['sell_pressure'] = np.where(df['close'] < df['open'], df['volume'], 0)
            df['order_flow'] = (df['buy_pressure'] - df['sell_pressure']) / df['volume']

            # 财务因子（基于技术分析模拟）
            # 1. 趋势强度
            df['trend_strength'] = abs(df['sma_5'] - df['sma_20']) / df['sma_20']
            df['trend_direction'] = np.where(df['sma_5'] > df['sma_20'], 1, -1)

            # 2. 市场情绪指标
            df['rsi'] = self._calculate_rsi(df['close'], 14)
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']

            # 宏观因子
            # 1. 市场环境指标
            df['market_regime'] = self._identify_market_regime(df)
            df['volatility_regime'] = np.where(df['volatility_20'] > df['volatility_20'].rolling(50).mean(), 1, 0)

            # 2. 相关性指标（简化版）
            df['price_volume_corr'] = df['close'].rolling(20).corr(df['volume'])

            return df

        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return df

    def _calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series(50, index=prices.index)  # 默认中性值

    def _identify_market_regime(self, df):
        """识别市场状态"""
        try:
            # 基于价格趋势和波动率识别市场状态
            # 0: 震荡, 1: 上涨, -1: 下跌
            trend = df['sma_5'] - df['sma_20']
            volatility = df['volatility_20']

            regime = np.where(
                (trend > 0) & (volatility < volatility.rolling(50).mean()), 1,  # 上涨趋势
                np.where(
                    (trend < 0) & (volatility < volatility.rolling(50).mean()), -1,  # 下跌趋势
                    0  # 震荡市场
                )
            )
            return pd.Series(regime, index=df.index)
        except:
            return pd.Series(0, index=df.index)  # 默认震荡市场

    def predict_next_price(self, df):
        """
        使用机器学习模型预测次日收盘价

        Args:
            df: 包含特征的DataFrame

        Returns:
            tuple: (预测价格, 预测置信度)
        """
        try:
            if self.ml_model is None or self.feature_scaler is None:
                # 使用简化的预测方法
                return self._simple_price_prediction(df)

            # 选择特征列
            feature_columns = [
                'returns', 'returns_5', 'returns_10', 'returns_20',
                'volatility_5', 'volatility_10', 'volatility_20',
                'high_low_ratio', 'close_position', 'price_acceleration',
                'volume_ratio', 'order_flow', 'trend_strength',
                'rsi', 'macd', 'macd_histogram', 'market_regime',
                'volatility_regime', 'price_volume_corr'
            ]

            # 过滤存在的特征列
            available_features = [col for col in feature_columns if col in df.columns]

            if len(available_features) < 5:
                return self._simple_price_prediction(df)

            # 准备特征数据
            feature_data = df[available_features].dropna()

            if len(feature_data) < 50:  # 数据不足
                return self._simple_price_prediction(df)

            # 检查是否需要重新训练模型
            if self._should_retrain_model():
                self._train_ml_model(df, available_features)

            # 进行预测
            latest_features = feature_data.iloc[-1:].values

            if hasattr(self.feature_scaler, 'transform'):
                latest_features_scaled = self.feature_scaler.transform(latest_features)
            else:
                latest_features_scaled = latest_features

            predicted_price = self.ml_model.predict(latest_features_scaled)[0]

            # 计算预测置信度（基于历史预测准确性）
            confidence = self._calculate_prediction_confidence()

            return predicted_price, confidence

        except Exception as e:
            self.logger.error(f"机器学习预测失败: {e}")
            return self._simple_price_prediction(df)

    def _simple_price_prediction(self, df):
        """简化的价格预测方法"""
        try:
            # 基于移动平均和动量的简单预测
            current_price = df['close'].iloc[-1]
            sma_5 = df['close'].rolling(5).mean().iloc[-1]
            sma_20 = df['close'].rolling(20).mean().iloc[-1]
            momentum = df['close'].pct_change(5).iloc[-1]

            # 简单的线性预测
            trend_factor = (sma_5 - sma_20) / sma_20
            momentum_factor = momentum * 0.5

            predicted_price = current_price * (1 + trend_factor + momentum_factor)
            confidence = 0.6  # 中等置信度

            return predicted_price, confidence

        except Exception as e:
            self.logger.error(f"简化预测失败: {e}")
            current_price = df['close'].iloc[-1] if len(df) > 0 else 0.21375
            return current_price * 1.001, 0.5  # 微小上涨预测

    def _should_retrain_model(self):
        """判断是否需要重新训练模型"""
        if self.last_retrain_date is None:
            return True

        days_since_retrain = (datetime.now() - self.last_retrain_date).days
        return days_since_retrain >= self.model_retrain_days

    def _train_ml_model(self, df, feature_columns):
        """训练机器学习模型"""
        try:
            if self.ml_model is None or not self.sklearn_available:
                return

            # 准备训练数据
            feature_data = df[feature_columns].dropna()

            if len(feature_data) < 100:  # 数据不足
                self.logger.warning("训练数据不足，跳过模型训练")
                return

            # 创建目标变量（次日收盘价）
            target = df['close'].shift(-1).dropna()

            # 对齐特征和目标数据
            min_length = min(len(feature_data), len(target))
            X = feature_data.iloc[:min_length].values
            y = target.iloc[:min_length].values

            # 分割训练和验证数据
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import mean_squared_error

            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )

            # 特征标准化
            X_train_scaled = self.feature_scaler.fit_transform(X_train)
            X_test_scaled = self.feature_scaler.transform(X_test)

            # 训练模型
            self.ml_model.fit(X_train_scaled, y_train)

            # 评估模型
            y_pred = self.ml_model.predict(X_test_scaled)
            mse = mean_squared_error(y_test, y_pred)

            self.last_retrain_date = datetime.now()
            self.logger.info(f"模型训练完成，MSE: {mse:.6f}")

        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")

    def _calculate_prediction_confidence(self):
        """计算预测置信度"""
        try:
            if len(self.prediction_history) < 10:
                return 0.7  # 默认置信度

            # 基于最近预测的准确性计算置信度
            recent_predictions = self.prediction_history[-10:]
            accuracies = []

            for pred_data in recent_predictions:
                predicted = pred_data['predicted']
                actual = pred_data['actual']
                if actual > 0:
                    accuracy = 1 - abs(predicted - actual) / actual
                    accuracies.append(max(0, accuracy))

            if accuracies:
                avg_accuracy = np.mean(accuracies)
                return min(0.95, max(0.5, avg_accuracy))
            else:
                return 0.7

        except Exception as e:
            self.logger.error(f"置信度计算失败: {e}")
            return 0.7

    def reconstruct_price_series(self, df, predicted_price, confidence):
        """
        重构价格序列

        将预测价格与历史数据融合，创建超前的价格序列

        Args:
            df: 历史价格数据
            predicted_price: 预测的次日收盘价
            confidence: 预测置信度

        Returns:
            DataFrame: 重构后的价格数据
        """
        try:
            # 复制原始数据
            reconstructed_df = df.copy()

            # 根据置信度调整预测价格的权重
            current_price = df['close'].iloc[-1]

            # 置信度越高，预测价格权重越大
            weight = confidence
            adjusted_predicted_price = (
                weight * predicted_price +
                (1 - weight) * current_price
            )

            # 创建重构的价格序列
            # 方法1: 简单替换最后一个价格
            reconstructed_df.loc[reconstructed_df.index[-1], 'close'] = adjusted_predicted_price

            # 方法2: 平滑过渡（更高级的方法）
            if len(reconstructed_df) >= 3:
                # 对最近几个价格点进行平滑调整
                recent_prices = reconstructed_df['close'].iloc[-3:].copy()

                # 计算调整因子
                price_change = adjusted_predicted_price - current_price

                # 逐步调整最近的价格
                for i in range(len(recent_prices)):
                    adjustment_factor = (i + 1) / len(recent_prices) * weight
                    recent_prices.iloc[i] += price_change * adjustment_factor

                # 更新重构数据
                reconstructed_df.loc[reconstructed_df.index[-3:], 'close'] = recent_prices

            # 记录预测历史
            self.prediction_history.append({
                'timestamp': datetime.now(),
                'predicted': predicted_price,
                'confidence': confidence,
                'actual': None  # 将在下次更新时填入实际价格
            })

            # 更新之前预测的实际价格
            if len(self.prediction_history) > 1:
                self.prediction_history[-2]['actual'] = current_price

            # 保持预测历史长度
            if len(self.prediction_history) > 100:
                self.prediction_history = self.prediction_history[-100:]

            return reconstructed_df

        except Exception as e:
            self.logger.error(f"价格序列重构失败: {e}")
            return df

    def calculate_fmacd(self, df):
        """
        计算FMACD指标

        基于重构的价格序列计算超前的MACD指标

        Args:
            df: 重构后的价格数据

        Returns:
            DataFrame: 添加了FMACD指标的数据
        """
        try:
            # 计算快速和慢速EMA
            df['fast_ema'] = df['close'].ewm(span=self.fast_period).mean()
            df['slow_ema'] = df['close'].ewm(span=self.slow_period).mean()

            # 计算FDIF（超前DIF）
            df['fdif'] = df['fast_ema'] - df['slow_ema']

            # 计算FDEA（超前DEA/信号线）
            df['fdea'] = df['fdif'].ewm(span=self.signal_period).mean()

            # 计算FMACD柱状图
            df['fmacd'] = df['fdif'] - df['fdea']

            # 计算传统MACD作为对比
            df['traditional_dif'] = df['fast_ema'] - df['slow_ema']
            df['traditional_dea'] = df['traditional_dif'].ewm(span=self.signal_period).mean()
            df['traditional_macd'] = df['traditional_dif'] - df['traditional_dea']

            # 计算信号
            df['fmacd_signal'] = 0
            df['signal_strength'] = 0

            for i in range(1, len(df)):
                # 金叉信号
                if (df['fdif'].iloc[i] > df['fdea'].iloc[i] and
                    df['fdif'].iloc[i-1] <= df['fdea'].iloc[i-1]):
                    df.iloc[i, df.columns.get_loc('fmacd_signal')] = 1
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 2

                # 死叉信号
                elif (df['fdif'].iloc[i] < df['fdea'].iloc[i] and
                      df['fdif'].iloc[i-1] >= df['fdea'].iloc[i-1]):
                    df.iloc[i, df.columns.get_loc('fmacd_signal')] = -1
                    df.iloc[i, df.columns.get_loc('signal_strength')] = -2

                # FMACD柱状图信号
                elif df['fmacd'].iloc[i] > 0 and df['fmacd'].iloc[i-1] <= 0:
                    df.iloc[i, df.columns.get_loc('fmacd_signal')] = 1
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 1

                elif df['fmacd'].iloc[i] < 0 and df['fmacd'].iloc[i-1] >= 0:
                    df.iloc[i, df.columns.get_loc('fmacd_signal')] = -1
                    df.iloc[i, df.columns.get_loc('signal_strength')] = -1

            return df

        except Exception as e:
            self.logger.error(f"FMACD计算失败: {e}")
            return df

    def generate_trading_signals(self, df):
        """
        生成交易信号

        结合FMACD信号和预测置信度生成最终交易决策

        Args:
            df: 包含FMACD指标的DataFrame

        Returns:
            DataFrame: 添加了最终交易信号的数据
        """
        try:
            # 获取最新的预测置信度
            latest_confidence = (
                self.prediction_history[-1]['confidence']
                if self.prediction_history else 0.7
            )

            # 初始化最终信号
            df['final_signal'] = 0
            df['signal_confidence'] = 0

            for i in range(len(df)):
                fmacd_signal = df['fmacd_signal'].iloc[i]
                signal_strength = df['signal_strength'].iloc[i]

                # 只有在预测置信度足够高时才发出信号
                if latest_confidence >= self.prediction_confidence:
                    if fmacd_signal != 0:
                        # 根据信号强度和置信度调整最终信号
                        confidence_factor = latest_confidence
                        strength_factor = abs(signal_strength) / 2.0

                        final_confidence = confidence_factor * strength_factor

                        if final_confidence >= 0.6:  # 最低信号阈值
                            df.iloc[i, df.columns.get_loc('final_signal')] = fmacd_signal
                            df.iloc[i, df.columns.get_loc('signal_confidence')] = final_confidence

            return df

        except Exception as e:
            self.logger.error(f"交易信号生成失败: {e}")
            return df

    def execute_trade(self, signal, current_price, timestamp, confidence):
        """
        执行交易

        Args:
            signal: 交易信号 (1: 买入, -1: 卖出, 0: 无操作)
            current_price: 当前价格
            timestamp: 时间戳
            confidence: 信号置信度
        """
        try:
            if signal == 1 and self.position <= 0:
                # 买入信号
                if self.position == -1:
                    # 先平空头仓位
                    self._close_position(current_price, timestamp, "平空")

                # 开多头仓位
                order = self.exchange.create_limit_buy_order(
                    self.symbol, self.amount, current_price
                )

                self.position = 1
                self.entry_price = current_price

                log_trade(self.symbol, "买入", self.amount, current_price, timestamp)
                self.logger.info(f"FMACD策略买入: {self.amount} @ {current_price}, 置信度: {confidence:.2f}")

            elif signal == -1 and self.position >= 0:
                # 卖出信号
                if self.position == 1:
                    # 先平多头仓位
                    self._close_position(current_price, timestamp, "平多")

                # 开空头仓位
                order = self.exchange.create_limit_sell_order(
                    self.symbol, self.amount, current_price
                )

                self.position = -1
                self.entry_price = current_price

                log_trade(self.symbol, "卖出", self.amount, current_price, timestamp)
                self.logger.info(f"FMACD策略卖出: {self.amount} @ {current_price}, 置信度: {confidence:.2f}")

        except Exception as e:
            self.logger.error(f"执行交易失败: {e}")

    def _close_position(self, current_price, timestamp, action):
        """平仓操作"""
        try:
            if self.position == 1:
                # 平多头仓位
                order = self.exchange.create_limit_sell_order(
                    self.symbol, self.amount, current_price
                )
                pnl = (current_price - self.entry_price) / self.entry_price * 100

            elif self.position == -1:
                # 平空头仓位
                order = self.exchange.create_limit_buy_order(
                    self.symbol, self.amount, current_price
                )
                pnl = (self.entry_price - current_price) / self.entry_price * 100

            log_trade(self.symbol, action, self.amount, current_price, timestamp, pnl)
            self.logger.info(f"FMACD策略{action}: {self.amount} @ {current_price}, 盈亏: {pnl:.2f}%")

            self.position = 0
            self.entry_price = 0

        except Exception as e:
            self.logger.error(f"平仓操作失败: {e}")

    def check_risk_management(self, current_price):
        """检查风险管理"""
        try:
            if self.position != 0 and self.entry_price > 0:
                if self.position == 1:
                    # 多头仓位
                    pnl_pct = (current_price - self.entry_price) / self.entry_price

                    # 止损
                    if pnl_pct <= -self.stop_loss_pct:
                        self._close_position(current_price, datetime.now(), "止损")
                        return True

                    # 止盈
                    if pnl_pct >= self.take_profit_pct:
                        self._close_position(current_price, datetime.now(), "止盈")
                        return True

                elif self.position == -1:
                    # 空头仓位
                    pnl_pct = (self.entry_price - current_price) / self.entry_price

                    # 止损
                    if pnl_pct <= -self.stop_loss_pct:
                        self._close_position(current_price, datetime.now(), "止损")
                        return True

                    # 止盈
                    if pnl_pct >= self.take_profit_pct:
                        self._close_position(current_price, datetime.now(), "止盈")
                        return True

            return False

        except Exception as e:
            self.logger.error(f"风险管理检查失败: {e}")
            return False

    def run_live(self, timeframe='1h'):
        """
        实时运行FMACD策略

        Args:
            timeframe: K线周期，默认1小时
        """
        self.running = True
        log_strategy_start("fmacd_strategy")
        self.logger.info(f"FMACD策略开始运行: {self.symbol}, 周期: {timeframe}")

        try:
            while self.running:
                # 获取最新市场数据
                df = self.get_market_data(timeframe, limit=max(200, self.slow_period + 50))

                if df is None or len(df) < self.slow_period:
                    self.logger.warning("市场数据不足，等待下次检查")
                    time.sleep(60)
                    continue

                # 提取特征
                df = self.extract_features(df)

                # 预测次日价格
                predicted_price, confidence = self.predict_next_price(df)

                # 重构价格序列
                reconstructed_df = self.reconstruct_price_series(df, predicted_price, confidence)

                # 计算FMACD指标
                fmacd_df = self.calculate_fmacd(reconstructed_df)

                # 生成交易信号
                signal_df = self.generate_trading_signals(fmacd_df)

                # 获取最新信号
                latest_signal = signal_df.iloc[-1]['final_signal']
                latest_price = signal_df.iloc[-1]['close']
                latest_timestamp = signal_df.iloc[-1]['timestamp']
                signal_confidence = signal_df.iloc[-1]['signal_confidence']

                # 记录信号信息
                if latest_signal != 0:
                    signal_type = "买入" if latest_signal == 1 else "卖出"
                    self.logger.info(f"FMACD信号: {signal_type}, 置信度: {signal_confidence:.2f}, "
                                   f"预测价格: {predicted_price:.6f}, 当前价格: {latest_price:.6f}")

                # 风险管理检查
                risk_triggered = self.check_risk_management(latest_price)

                # 执行交易（如果没有触发风险管理）
                if not risk_triggered:
                    self.execute_trade(latest_signal, latest_price, latest_timestamp, signal_confidence)

                # 更新历史数据
                self.fmacd_history.append(signal_df.iloc[-1]['fmacd'])
                self.fdif_history.append(signal_df.iloc[-1]['fdif'])
                self.fdea_history.append(signal_df.iloc[-1]['fdea'])
                self.price_history.append(latest_price)

                # 保持历史数据长度
                max_history = 200
                if len(self.fmacd_history) > max_history:
                    self.fmacd_history = self.fmacd_history[-max_history:]
                    self.fdif_history = self.fdif_history[-max_history:]
                    self.fdea_history = self.fdea_history[-max_history:]
                    self.price_history = self.price_history[-max_history:]

                # 等待下次检查
                time.sleep(300)  # 5分钟检查一次

        except Exception as e:
            error_details = error_handler.handle_error(e, ErrorCode.STRATEGY_EXECUTION_FAILED)
            self.logger.error(f"FMACD策略运行失败: {error_details['message']}")
            self.running = False

    def run_backtest(self, start_date, end_date, timeframe='1h'):
        """
        回测FMACD策略

        Args:
            start_date: 开始日期
            end_date: 结束日期
            timeframe: K线周期

        Returns:
            dict: 回测结果
        """
        try:
            self.logger.info(f"开始FMACD策略回测: {start_date} 到 {end_date}")

            # 获取历史数据
            df = self.get_market_data(timeframe, limit=1000)

            if df is None or len(df) < self.slow_period + 50:
                self.logger.error("历史数据不足，无法进行回测")
                return None

            # 提取特征
            df = self.extract_features(df)

            # 回测统计
            trades = []
            total_return = 0
            win_trades = 0
            lose_trades = 0
            max_drawdown = 0
            peak_value = 0

            position = 0
            entry_price = 0

            # 逐步回测
            for i in range(self.slow_period + 50, len(df)):
                current_data = df.iloc[:i+1].copy()

                # 预测价格（使用历史数据模拟）
                predicted_price, confidence = self.predict_next_price(current_data)

                # 重构价格序列
                reconstructed_data = self.reconstruct_price_series(current_data, predicted_price, confidence)

                # 计算FMACD
                fmacd_data = self.calculate_fmacd(reconstructed_data)

                # 生成信号
                signal_data = self.generate_trading_signals(fmacd_data)

                current_price = signal_data.iloc[-1]['close']
                signal = signal_data.iloc[-1]['final_signal']
                timestamp = signal_data.iloc[-1]['timestamp']
                signal_confidence = signal_data.iloc[-1]['signal_confidence']

                # 执行交易逻辑
                if signal == 1 and position <= 0:
                    if position == -1:
                        # 平空头
                        pnl = (entry_price - current_price) / entry_price
                        total_return += pnl
                        trades.append({
                            'type': '平空',
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'pnl': pnl,
                            'confidence': signal_confidence,
                            'timestamp': timestamp
                        })
                        if pnl > 0:
                            win_trades += 1
                        else:
                            lose_trades += 1

                    # 开多头
                    position = 1
                    entry_price = current_price

                elif signal == -1 and position >= 0:
                    if position == 1:
                        # 平多头
                        pnl = (current_price - entry_price) / entry_price
                        total_return += pnl
                        trades.append({
                            'type': '平多',
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'pnl': pnl,
                            'confidence': signal_confidence,
                            'timestamp': timestamp
                        })
                        if pnl > 0:
                            win_trades += 1
                        else:
                            lose_trades += 1

                    # 开空头
                    position = -1
                    entry_price = current_price

                # 计算最大回撤
                current_value = 1 + total_return
                if current_value > peak_value:
                    peak_value = current_value
                else:
                    drawdown = (peak_value - current_value) / peak_value
                    max_drawdown = max(max_drawdown, drawdown)

            # 计算回测结果
            total_trades = win_trades + lose_trades
            win_rate = win_trades / total_trades if total_trades > 0 else 0

            # 计算平均置信度
            avg_confidence = np.mean([trade['confidence'] for trade in trades]) if trades else 0

            result = {
                'total_return': total_return * 100,  # 转换为百分比
                'total_trades': total_trades,
                'win_trades': win_trades,
                'lose_trades': lose_trades,
                'win_rate': win_rate * 100,
                'max_drawdown': max_drawdown * 100,
                'avg_confidence': avg_confidence,
                'trades': trades
            }

            self.logger.info(f"FMACD策略回测完成: 总收益 {result['total_return']:.2f}%, "
                           f"胜率 {result['win_rate']:.2f}%, 最大回撤 {result['max_drawdown']:.2f}%, "
                           f"平均置信度 {result['avg_confidence']:.2f}")

            return result

        except Exception as e:
            self.logger.error(f"FMACD策略回测失败: {e}")
            return None

    def get_strategy_status(self):
        """获取策略状态"""
        return {
            'running': self.running,
            'symbol': self.symbol,
            'position': self.position,
            'entry_price': self.entry_price,
            'fast_period': self.fast_period,
            'slow_period': self.slow_period,
            'signal_period': self.signal_period,
            'stop_loss_pct': self.stop_loss_pct,
            'take_profit_pct': self.take_profit_pct,
            'model_retrain_days': self.model_retrain_days,
            'prediction_confidence': self.prediction_confidence,
            'fmacd_history_length': len(self.fmacd_history),
            'latest_fmacd': self.fmacd_history[-1] if self.fmacd_history else 0,
            'latest_fdif': self.fdif_history[-1] if self.fdif_history else 0,
            'latest_fdea': self.fdea_history[-1] if self.fdea_history else 0,
            'prediction_history_length': len(self.prediction_history),
            'last_retrain_date': self.last_retrain_date.strftime('%Y-%m-%d') if self.last_retrain_date else None
        }

    def stop(self):
        """停止FMACD策略"""
        self.running = False
        self.logger.info("FMACD策略已停止")
        log_strategy_stop("fmacd_strategy")
