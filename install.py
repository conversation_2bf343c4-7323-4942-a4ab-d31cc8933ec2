#!/usr/bin/env python3
"""
量化交易系统安装脚本
自动检查和安装依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("量化交易系统安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"Python版本: {sys.version}")
    print()
    
    # 读取requirements.txt
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"错误: 找不到 {requirements_file} 文件")
        return False
    
    with open(requirements_file, 'r') as f:
        packages = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    print("检查依赖包...")
    print("-" * 40)
    
    missing_packages = []
    for package in packages:
        # 提取包名（去掉版本号）
        package_name = package.split('>=')[0].split('==')[0].split('<')[0]
        
        if check_package(package_name):
            print(f"✓ {package_name} - 已安装")
        else:
            print(f"✗ {package_name} - 未安装")
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n所有依赖包已安装!")
        print("可以运行: python main.py")
        return True
    
    print(f"\n需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    # 询问是否安装
    response = input("\n是否现在安装这些包? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("安装已取消")
        return False
    
    print("\n开始安装依赖包...")
    print("-" * 40)
    
    failed_packages = []
    for package in missing_packages:
        print(f"正在安装 {package}...")
        if install_package(package):
            print(f"✓ {package} 安装成功")
        else:
            print(f"✗ {package} 安装失败")
            failed_packages.append(package)
    
    print("\n安装完成!")
    print("-" * 40)
    
    if failed_packages:
        print("以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装这些包:")
        print(f"pip install {' '.join(failed_packages)}")
        return False
    else:
        print("所有依赖包安装成功!")
        print("\n现在可以运行量化交易系统:")
        print("python main.py")
        return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n" + "=" * 60)
            print("安装完成! 系统已准备就绪。")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("安装未完成，请检查错误信息。")
            print("=" * 60)
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
