#!/usr/bin/env python3
"""
用户友好输入组件模块

提供带有详细说明、建议范围、使用示例和风险提示的输入组件，
让没有量化交易经验的用户也能安全、正确地配置系统参数。

主要功能：
1. 智能输入框：带有实时验证和提示
2. 参数说明：详细的中文说明和使用建议
3. 范围提示：建议的数值范围和安全区间
4. 示例展示：实际使用示例和最佳实践
5. 风险警告：重要的风险提示和注意事项
"""

import tkinter as tk
from tkinter import ttk, messagebox
import re
from typing import Dict, List, Optional, Callable, Any

class ParameterInfo:
    """参数信息类，包含参数的所有说明信息"""
    
    def __init__(self, 
                 name: str,
                 description: str,
                 suggested_range: str,
                 examples: List[str],
                 risk_warnings: List[str],
                 validation_func: Optional[Callable] = None,
                 default_value: str = "",
                 unit: str = ""):
        self.name = name
        self.description = description
        self.suggested_range = suggested_range
        self.examples = examples
        self.risk_warnings = risk_warnings
        self.validation_func = validation_func
        self.default_value = default_value
        self.unit = unit

class UserFriendlyInput:
    """用户友好输入组件"""
    
    def __init__(self, parent, param_info: ParameterInfo, width: int = 15):
        self.parent = parent
        self.param_info = param_info
        self.width = width
        
        # 创建主框架
        self.frame = ttk.Frame(parent)
        
        # 创建变量
        self.var = tk.StringVar(value=param_info.default_value)
        self.var.trace('w', self._on_value_change)
        
        # 创建组件
        self._create_widgets()
        
        # 验证状态
        self.is_valid = True
        self.last_validation_message = ""
    
    def _create_widgets(self):
        """创建输入组件"""
        # 主标签和输入框框架
        input_frame = ttk.Frame(self.frame)
        input_frame.pack(fill=tk.X, pady=2)
        
        # 参数标签
        label_text = f"{self.param_info.name}:"
        if self.param_info.unit:
            label_text = f"{self.param_info.name}({self.param_info.unit}):"
        
        self.label = ttk.Label(input_frame, text=label_text, width=12)
        self.label.pack(side=tk.LEFT, padx=(0, 5))
        
        # 输入框
        self.entry = ttk.Entry(input_frame, textvariable=self.var, width=self.width)
        self.entry.pack(side=tk.LEFT, padx=(0, 5))
        
        # 帮助按钮
        self.help_btn = ttk.Button(input_frame, text="?", width=3, 
                                  command=self._show_help)
        self.help_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态指示器
        self.status_label = ttk.Label(input_frame, text="✓", foreground="green", width=2)
        self.status_label.pack(side=tk.LEFT)
        
        # 说明框架（可折叠）
        self.info_frame = ttk.Frame(self.frame)
        self.info_visible = False
        
        # 快速提示标签
        self.quick_tip = ttk.Label(self.frame, 
                                  text=f"建议范围: {self.param_info.suggested_range}",
                                  foreground="blue", font=("Arial", 8))
        self.quick_tip.pack(fill=tk.X, padx=(15, 0))
        
        # 绑定事件
        self.entry.bind('<FocusIn>', self._on_focus_in)
        self.entry.bind('<FocusOut>', self._on_focus_out)
    
    def _create_info_panel(self):
        """创建详细信息面板"""
        if hasattr(self, 'info_panel'):
            return
        
        self.info_panel = ttk.LabelFrame(self.info_frame, text=f"{self.param_info.name} - 详细说明")
        self.info_panel.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 描述
        desc_frame = ttk.Frame(self.info_panel)
        desc_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(desc_frame, text="📝 参数说明:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        ttk.Label(desc_frame, text=self.param_info.description, 
                 wraplength=400, justify=tk.LEFT).pack(anchor=tk.W, padx=10)
        
        # 建议范围
        range_frame = ttk.Frame(self.info_panel)
        range_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(range_frame, text="📊 建议范围:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        ttk.Label(range_frame, text=self.param_info.suggested_range, 
                 foreground="blue").pack(anchor=tk.W, padx=10)
        
        # 使用示例
        if self.param_info.examples:
            example_frame = ttk.Frame(self.info_panel)
            example_frame.pack(fill=tk.X, padx=5, pady=2)
            ttk.Label(example_frame, text="💡 使用示例:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
            for example in self.param_info.examples:
                ttk.Label(example_frame, text=f"• {example}", 
                         foreground="green").pack(anchor=tk.W, padx=10)
        
        # 风险提示
        if self.param_info.risk_warnings:
            warning_frame = ttk.Frame(self.info_panel)
            warning_frame.pack(fill=tk.X, padx=5, pady=2)
            ttk.Label(warning_frame, text="⚠️ 风险提示:", font=("Arial", 9, "bold"), 
                     foreground="red").pack(anchor=tk.W)
            for warning in self.param_info.risk_warnings:
                ttk.Label(warning_frame, text=f"• {warning}", 
                         foreground="red", wraplength=400, justify=tk.LEFT).pack(anchor=tk.W, padx=10)
    
    def _show_help(self):
        """显示帮助信息"""
        self._create_info_panel()
        
        if not self.info_visible:
            self.info_frame.pack(fill=tk.X, pady=5)
            self.info_visible = True
            self.help_btn.configure(text="▲")
        else:
            self.info_frame.pack_forget()
            self.info_visible = False
            self.help_btn.configure(text="?")
    
    def _on_focus_in(self, event):
        """输入框获得焦点时"""
        # 显示快速提示
        if self.param_info.examples:
            example = self.param_info.examples[0]
            self.quick_tip.configure(text=f"示例: {example}")
    
    def _on_focus_out(self, event):
        """输入框失去焦点时"""
        # 恢复范围提示
        self.quick_tip.configure(text=f"建议范围: {self.param_info.suggested_range}")
        # 执行验证
        self._validate_value()
    
    def _on_value_change(self, *args):
        """值变化时的回调"""
        # 实时验证（延迟执行避免频繁验证）
        if hasattr(self, '_validation_after_id'):
            self.parent.after_cancel(self._validation_after_id)
        self._validation_after_id = self.parent.after(500, self._validate_value)
    
    def _validate_value(self):
        """验证输入值"""
        value = self.var.get().strip()
        
        if not value:
            self._set_status("empty", "请输入值")
            return False
        
        # 使用自定义验证函数
        if self.param_info.validation_func:
            try:
                is_valid, message = self.param_info.validation_func(value)
                if is_valid:
                    self._set_status("valid", "输入有效")
                    return True
                else:
                    self._set_status("invalid", message)
                    return False
            except Exception as e:
                self._set_status("error", f"验证错误: {str(e)}")
                return False
        else:
            # 默认验证（检查是否为数字）
            try:
                float(value)
                self._set_status("valid", "输入有效")
                return True
            except ValueError:
                self._set_status("invalid", "请输入有效数字")
                return False
    
    def _set_status(self, status: str, message: str):
        """设置状态指示器"""
        self.last_validation_message = message
        
        if status == "valid":
            self.status_label.configure(text="✓", foreground="green")
            self.entry.configure(style="")
            self.is_valid = True
        elif status == "invalid":
            self.status_label.configure(text="✗", foreground="red")
            self.entry.configure(style="Invalid.TEntry")
            self.is_valid = False
        elif status == "empty":
            self.status_label.configure(text="○", foreground="gray")
            self.entry.configure(style="")
            self.is_valid = False
        elif status == "error":
            self.status_label.configure(text="!", foreground="orange")
            self.entry.configure(style="Invalid.TEntry")
            self.is_valid = False
    
    def get_value(self):
        """获取输入值"""
        return self.var.get().strip()
    
    def set_value(self, value: str):
        """设置输入值"""
        self.var.set(str(value))
    
    def is_value_valid(self):
        """检查值是否有效"""
        return self.is_valid
    
    def get_validation_message(self):
        """获取验证消息"""
        return self.last_validation_message
    
    def pack(self, **kwargs):
        """打包组件"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)

# 预定义的参数信息
PARAMETER_DEFINITIONS = {
    # 网格交易策略参数
    "grid_symbol": ParameterInfo(
        name="交易对",
        description="选择要进行网格交易的数字货币交易对。建议选择波动适中、流动性好的主流币种。",
        suggested_range="主流币种如 BTC/USDT, ETH/USDT, CFX/USDT",
        examples=["CFX/USDT", "BTC/USDT", "ETH/USDT", "CFX-USDT-SWAP"],
        risk_warnings=[
            "避免选择波动过大的小币种，可能导致巨大亏损",
            "确保选择的交易对有足够的流动性",
            "新币种风险较高，建议选择成熟的主流币种"
        ]
    ),
    
    "grid_base_price": ParameterInfo(
        name="基准价格",
        description="网格交易的中心价格，通常设置为当前市场价格或预期的平均价格。系统会在此价格上下设置买卖网格。",
        suggested_range="当前市价的 ±5% 范围内",
        examples=["0.21375 (CFX当前价格)", "30000 (BTC参考价格)", "2000 (ETH参考价格)"],
        risk_warnings=[
            "设置过高可能导致长期无法买入",
            "设置过低可能导致长期无法卖出",
            "建议根据技术分析确定合理的中心价格"
        ],
        validation_func=lambda x: (float(x) > 0, "价格必须大于0") if x.replace('.', '').isdigit() else (False, "请输入有效的价格数字")
    ),
    
    "grid_spacing": ParameterInfo(
        name="网格间距",
        description="相邻网格之间的价格间距（百分比）。间距越小，交易频率越高但单次利润越小；间距越大，交易频率越低但单次利润越大。",
        suggested_range="0.5% - 5%（新手建议1% - 2%）",
        examples=["1.0 (1%间距，适合稳定币种)", "2.0 (2%间距，平衡选择)", "0.5 (0.5%间距，高频交易)"],
        risk_warnings=[
            "间距过小可能导致频繁交易，手续费成本高",
            "间距过大可能错过较多交易机会",
            "建议根据币种波动性调整间距大小"
        ],
        validation_func=lambda x: (0.1 <= float(x) <= 20, "网格间距建议在0.1%-20%之间") if x.replace('.', '').isdigit() else (False, "请输入有效的百分比数字"),
        unit="%"
    ),
    
    "grid_count": ParameterInfo(
        name="网格数量",
        description="在基准价格上下各设置多少个网格。网格数量越多，覆盖的价格范围越广，但需要的资金也越多。",
        suggested_range="5 - 30个（新手建议10 - 15个）",
        examples=["10 (适合新手，资金需求适中)", "20 (覆盖范围更广)", "5 (资金有限时的选择)"],
        risk_warnings=[
            "网格数量过多需要大量资金支持",
            "网格数量过少可能无法充分利用价格波动",
            "建议根据可用资金合理设置网格数量"
        ],
        validation_func=lambda x: (2 <= int(x) <= 100, "网格数量建议在2-100个之间") if x.isdigit() else (False, "请输入有效的整数"),
        unit="个"
    ),
    
    "grid_order_amount": ParameterInfo(
        name="交易数量",
        description="每个网格的交易数量。这决定了每次买入或卖出的数量，影响资金利用率和风险控制。",
        suggested_range="根据总资金的1% - 5%设置",
        examples=["100 (CFX数量)", "0.001 (BTC数量)", "0.1 (ETH数量)"],
        risk_warnings=[
            "单次交易量过大可能增加风险",
            "单次交易量过小可能影响收益",
            "建议不超过总资金的5%用于单个网格"
        ],
        validation_func=lambda x: (float(x) > 0, "交易数量必须大于0") if x.replace('.', '').isdigit() else (False, "请输入有效的数量")
    ),
    
    # 移动平均线策略参数
    "ma_short_window": ParameterInfo(
        name="短期均线",
        description="短期移动平均线的周期数。用于捕捉短期价格趋势，周期越短对价格变化越敏感。",
        suggested_range="5 - 20（常用10、12、15）",
        examples=["10 (经典短期均线)", "5 (更敏感的短期信号)", "15 (较稳定的短期趋势)"],
        risk_warnings=[
            "周期过短可能产生过多假信号",
            "周期过长可能错过短期机会",
            "建议结合市场特点选择合适周期"
        ],
        validation_func=lambda x: (1 <= int(x) <= 100, "短期均线周期建议在1-100之间") if x.isdigit() else (False, "请输入有效的整数"),
        unit="周期"
    ),
    
    "ma_long_window": ParameterInfo(
        name="长期均线",
        description="长期移动平均线的周期数。用于确定主要趋势方向，必须大于短期均线周期。",
        suggested_range="20 - 60（常用30、50）",
        examples=["30 (经典长期均线)", "50 (更稳定的趋势判断)", "20 (相对较短的长期均线)"],
        risk_warnings=[
            "必须大于短期均线周期",
            "周期过长可能反应迟缓",
            "周期过短可能失去趋势判断意义"
        ],
        validation_func=lambda x: (5 <= int(x) <= 200, "长期均线周期建议在5-200之间") if x.isdigit() else (False, "请输入有效的整数"),
        unit="周期"
    ),
    
    # RSI策略参数
    "rsi_period": ParameterInfo(
        name="RSI周期",
        description="RSI（相对强弱指数）的计算周期。标准设置为14，用于判断超买超卖状态。",
        suggested_range="10 - 21（标准为14）",
        examples=["14 (标准RSI周期)", "21 (更平滑的RSI)", "10 (更敏感的RSI)"],
        risk_warnings=[
            "周期过短可能产生过多信号",
            "周期过长可能反应迟缓",
            "建议使用标准的14周期"
        ],
        validation_func=lambda x: (5 <= int(x) <= 50, "RSI周期建议在5-50之间") if x.isdigit() else (False, "请输入有效的整数"),
        unit="周期"
    ),
    
    "rsi_oversold": ParameterInfo(
        name="超卖阈值",
        description="RSI超卖阈值，当RSI低于此值时认为超卖，可能是买入信号。",
        suggested_range="20 - 35（标准为30）",
        examples=["30 (标准超卖线)", "25 (更严格的超卖)", "35 (较宽松的超卖)"],
        risk_warnings=[
            "设置过低可能错过买入机会",
            "设置过高可能产生假信号",
            "建议结合市场情况调整"
        ],
        validation_func=lambda x: (10 <= float(x) <= 50, "超卖阈值建议在10-50之间") if x.replace('.', '').isdigit() else (False, "请输入有效数字")
    ),
    
    "rsi_overbought": ParameterInfo(
        name="超买阈值",
        description="RSI超买阈值，当RSI高于此值时认为超买，可能是卖出信号。必须大于超卖阈值。",
        suggested_range="65 - 80（标准为70）",
        examples=["70 (标准超买线)", "75 (更严格的超买)", "65 (较宽松的超买)"],
        risk_warnings=[
            "必须大于超卖阈值",
            "设置过高可能错过卖出机会",
            "设置过低可能产生假信号"
        ],
        validation_func=lambda x: (50 <= float(x) <= 90, "超买阈值建议在50-90之间") if x.replace('.', '').isdigit() else (False, "请输入有效数字")
    ),

    # AO指标策略参数
    "ao_short_period": ParameterInfo(
        name="AO短期周期",
        description="AO指标的短期简单移动平均线周期，代表短期市场动能。标准设置为5日，用于捕捉短期价格动量变化。",
        suggested_range="3 - 10（标准为5）",
        examples=["5 (标准AO短期周期)", "3 (更敏感的短期周期)", "7 (更平滑的短期周期)"],
        risk_warnings=[
            "周期过短会增加市场噪音，可能产生过多假信号",
            "必须确保短期周期小于长期周期",
            "建议与长期周期保持合理比例（约1:7）"
        ],
        validation_func=lambda x: (1 <= int(x) <= 20, "短期周期建议在1-20之间") if x.isdigit() else (False, "请输入有效的整数")
    ),

    "ao_long_period": ParameterInfo(
        name="AO长期周期",
        description="AO指标的长期简单移动平均线周期，代表长期市场趋势。标准设置为34日，用于识别主要趋势方向。",
        suggested_range="20 - 50（标准为34）",
        examples=["34 (标准AO长期周期)", "21 (较敏感的长期周期)", "50 (更平滑的长期周期)"],
        risk_warnings=[
            "周期过长可能错过短期交易机会",
            "必须确保长期周期大于短期周期",
            "建议根据交易时间框架调整周期长度"
        ],
        validation_func=lambda x: (10 <= int(x) <= 100, "长期周期建议在10-100之间") if x.isdigit() else (False, "请输入有效的整数")
    ),

    "ao_amount": ParameterInfo(
        name="AO交易数量",
        description="每次AO信号触发时的交易数量。建议根据账户资金规模和风险承受能力设定合理的交易数量。",
        suggested_range="根据资金规模：小额10-100，中等100-1000",
        examples=["100 (小额测试交易)", "500 (中等仓位交易)", "1000 (较大仓位交易)"],
        risk_warnings=[
            "交易数量过大可能增加单笔交易风险",
            "确保有足够资金支持多次交易",
            "建议单笔交易不超过总资金的5-10%"
        ],
        validation_func=lambda x: (float(x) > 0, "交易数量必须大于0") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    ),

    "ao_stop_loss": ParameterInfo(
        name="AO止损百分比",
        description="AO策略的止损百分比，用于控制单笔交易的最大损失。合理的止损设置是风险管理的关键。",
        suggested_range="1 - 5%（建议3%）",
        examples=["3% (平衡的止损设置)", "2% (保守的止损设置)", "5% (激进的止损设置)"],
        risk_warnings=[
            "止损过小可能导致频繁触发，增加交易成本",
            "止损过大可能造成较大单笔损失",
            "建议根据市场波动性调整止损幅度"
        ],
        validation_func=lambda x: (0.5 <= float(x) <= 20, "止损百分比建议在0.5-20%之间") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    ),

    "ao_take_profit": ParameterInfo(
        name="AO止盈百分比",
        description="AO策略的止盈百分比，用于锁定交易利润。合理的止盈设置有助于保护已获得的收益。",
        suggested_range="3 - 10%（建议6%）",
        examples=["6% (平衡的止盈设置)", "4% (保守的止盈设置)", "8% (激进的止盈设置)"],
        risk_warnings=[
            "止盈过小可能错过大幅行情的额外收益",
            "止盈过大可能导致利润回吐",
            "建议止盈幅度大于止损幅度（风险回报比>1:1）"
        ],
        validation_func=lambda x: (1 <= float(x) <= 50, "止盈百分比建议在1-50%之间") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    ),

    # FMACD策略参数
    "fmacd_fast_period": ParameterInfo(
        name="FMACD快速周期",
        description="FMACD指标的快速EMA周期，用于计算短期价格趋势。标准设置为12日，影响信号的敏感度。",
        suggested_range="8 - 20（标准为12）",
        examples=["12 (标准MACD快速周期)", "8 (更敏感的快速周期)", "16 (更平滑的快速周期)"],
        risk_warnings=[
            "周期过短会增加假信号，但反应更快",
            "必须确保快速周期小于慢速周期",
            "建议与慢速周期保持合理比例（约1:2）"
        ],
        validation_func=lambda x: (5 <= int(x) <= 50, "快速周期建议在5-50之间") if x.isdigit() else (False, "请输入有效的整数")
    ),

    "fmacd_slow_period": ParameterInfo(
        name="FMACD慢速周期",
        description="FMACD指标的慢速EMA周期，用于计算长期价格趋势。标准设置为26日，提供趋势方向参考。",
        suggested_range="20 - 40（标准为26）",
        examples=["26 (标准MACD慢速周期)", "20 (较敏感的慢速周期)", "34 (更平滑的慢速周期)"],
        risk_warnings=[
            "周期过长可能错过短期机会",
            "必须确保慢速周期大于快速周期",
            "影响FMACD的整体趋势判断"
        ],
        validation_func=lambda x: (10 <= int(x) <= 100, "慢速周期建议在10-100之间") if x.isdigit() else (False, "请输入有效的整数")
    ),

    "fmacd_signal_period": ParameterInfo(
        name="FMACD信号周期",
        description="FMACD信号线的EMA周期，用于平滑DIF线并生成交易信号。标准设置为9日。",
        suggested_range="6 - 15（标准为9）",
        examples=["9 (标准MACD信号周期)", "6 (更敏感的信号线)", "12 (更平滑的信号线)"],
        risk_warnings=[
            "周期过短可能产生过多交易信号",
            "周期过长可能延迟信号确认",
            "直接影响金叉死叉信号的时机"
        ],
        validation_func=lambda x: (3 <= int(x) <= 30, "信号周期建议在3-30之间") if x.isdigit() else (False, "请输入有效的整数")
    ),

    "fmacd_amount": ParameterInfo(
        name="FMACD交易数量",
        description="每次FMACD信号触发时的交易数量。建议根据账户资金规模和机器学习模型的预测置信度设定。",
        suggested_range="根据资金规模：小额10-100，中等100-1000",
        examples=["100 (小额测试交易)", "500 (中等仓位交易)", "1000 (较大仓位交易)"],
        risk_warnings=[
            "交易数量应考虑预测置信度",
            "机器学习策略建议保守的仓位管理",
            "建议单笔交易不超过总资金的3-8%"
        ],
        validation_func=lambda x: (float(x) > 0, "交易数量必须大于0") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    ),

    "fmacd_stop_loss": ParameterInfo(
        name="FMACD止损百分比",
        description="FMACD策略的止损百分比。由于使用机器学习预测，建议设置相对保守的止损。",
        suggested_range="2 - 5%（建议3%）",
        examples=["3% (平衡的止损设置)", "2% (保守的止损设置)", "4% (适中的止损设置)"],
        risk_warnings=[
            "机器学习预测存在不确定性，建议保守止损",
            "止损过小可能被市场噪音触发",
            "应结合预测置信度调整止损幅度"
        ],
        validation_func=lambda x: (0.5 <= float(x) <= 20, "止损百分比建议在0.5-20%之间") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    ),

    "fmacd_take_profit": ParameterInfo(
        name="FMACD止盈百分比",
        description="FMACD策略的止盈百分比。可以根据机器学习模型的预测置信度动态调整止盈目标。",
        suggested_range="4 - 10%（建议6%）",
        examples=["6% (平衡的止盈设置)", "4% (保守的止盈设置)", "8% (激进的止盈设置)"],
        risk_warnings=[
            "高置信度预测可以设置更高的止盈目标",
            "止盈过小可能错过机器学习预测的大幅行情",
            "建议风险回报比至少1:2"
        ],
        validation_func=lambda x: (1 <= float(x) <= 50, "止盈百分比建议在1-50%之间") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    ),

    "fmacd_retrain_days": ParameterInfo(
        name="模型重训练间隔",
        description="机器学习模型的重新训练间隔天数。定期重训练有助于模型适应市场变化。",
        suggested_range="15 - 45天（建议30天）",
        examples=["30天 (标准重训练间隔)", "15天 (频繁重训练)", "45天 (较少重训练)"],
        risk_warnings=[
            "重训练过于频繁可能导致过拟合",
            "重训练间隔过长可能无法适应市场变化",
            "重训练需要足够的历史数据"
        ],
        validation_func=lambda x: (7 <= int(x) <= 90, "重训练间隔建议在7-90天之间") if x.isdigit() else (False, "请输入有效的整数")
    ),

    "fmacd_confidence": ParameterInfo(
        name="预测置信度阈值",
        description="机器学习模型预测的最低置信度阈值。只有当预测置信度超过此阈值时才会发出交易信号。",
        suggested_range="0.6 - 0.8（建议0.7）",
        examples=["0.7 (平衡的置信度要求)", "0.6 (较宽松的置信度)", "0.8 (严格的置信度要求)"],
        risk_warnings=[
            "置信度过低可能导致错误交易",
            "置信度过高可能错过交易机会",
            "应根据模型表现动态调整"
        ],
        validation_func=lambda x: (0.5 <= float(x) <= 0.95, "预测置信度建议在0.5-0.95之间") if x.replace('.', '').isdigit() else (False, "请输入有效的数字")
    )
}

def create_user_friendly_input(parent, param_key: str, **kwargs) -> UserFriendlyInput:
    """创建用户友好输入组件的便捷函数"""
    if param_key not in PARAMETER_DEFINITIONS:
        raise ValueError(f"未找到参数定义: {param_key}")
    
    param_info = PARAMETER_DEFINITIONS[param_key]
    return UserFriendlyInput(parent, param_info, **kwargs)
