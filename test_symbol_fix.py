#!/usr/bin/env python3
"""
测试交易对格式修复
验证参数验证器是否支持多种交易对格式
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_symbol_validation():
    """测试交易对验证功能"""
    print("🔍 测试交易对验证功能...")
    
    try:
        from parameter_validator import ParameterValidator
        
        validator = ParameterValidator()
        
        # 测试用例：各种格式的交易对
        test_cases = [
            # 现货格式
            ('CFX/USDT', True, '现货标准格式'),
            ('BTC/USDT', True, '现货标准格式'),
            ('ETH/BTC', True, '现货标准格式'),
            
            # 永续合约格式
            ('CFX-USDT-SWAP', True, '永续合约格式'),
            ('BTC-USDT-SWAP', True, '永续合约格式'),
            ('ETH-USDT-SWAP', True, '永续合约格式'),
            
            # PERP格式
            ('CFX-USDT-PERP', True, 'PERP永续合约格式'),
            ('BTC-USDT-PERP', True, 'PERP永续合约格式'),
            
            # 期货合约格式
            ('BTC-USD-240329', True, '期货合约格式'),
            ('ETH-USD-241225', True, '期货合约格式'),
            
            # 简化格式
            ('CFX-USDT', True, '简化格式'),
            ('BTC-USDT', True, '简化格式'),
            
            # 无效格式
            ('INVALID', False, '无效格式'),
            ('CFX_USDT', False, '下划线格式'),
            ('CFX', False, '单一货币'),
            ('', False, '空字符串'),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for symbol, should_pass, description in test_cases:
            try:
                validator.validate_symbol(symbol)
                if should_pass:
                    print(f"  ✅ {symbol} - {description}")
                    passed += 1
                else:
                    print(f"  ❌ {symbol} - {description} (应该失败但通过了)")
            except Exception as e:
                if not should_pass:
                    print(f"  ✅ {symbol} - {description} (正确拒绝)")
                    passed += 1
                else:
                    print(f"  ❌ {symbol} - {description} (应该通过但失败了): {e}")
        
        print(f"\n📊 验证测试结果: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"❌ 交易对验证测试失败: {e}")
        return False

def test_symbol_normalization():
    """测试交易对标准化功能"""
    print("\n🔍 测试交易对标准化功能...")
    
    try:
        from parameter_validator import ParameterValidator
        
        validator = ParameterValidator()
        
        # 测试用例：标准化转换
        test_cases = [
            ('CFX-USDT-SWAP', 'CFX/USDT'),
            ('BTC-USDT-SWAP', 'BTC/USDT'),
            ('ETH-USDT-PERP', 'ETH/USDT'),
            ('BTC-USD-240329', 'BTC/USD'),
            ('CFX-USDT', 'CFX/USDT'),
            ('BTC/USDT', 'BTC/USDT'),
            ('cfx-usdt-swap', 'CFX/USDT'),  # 小写测试
        ]
        
        passed = 0
        total = len(test_cases)
        
        for input_symbol, expected_output in test_cases:
            try:
                result = validator.normalize_symbol(input_symbol)
                if result == expected_output:
                    print(f"  ✅ {input_symbol} -> {result}")
                    passed += 1
                else:
                    print(f"  ❌ {input_symbol} -> {result} (期望: {expected_output})")
            except Exception as e:
                print(f"  ❌ {input_symbol} 标准化失败: {e}")
        
        print(f"\n📊 标准化测试结果: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"❌ 交易对标准化测试失败: {e}")
        return False

def test_symbol_type_detection():
    """测试交易对类型检测功能"""
    print("\n🔍 测试交易对类型检测功能...")
    
    try:
        from parameter_validator import ParameterValidator
        
        validator = ParameterValidator()
        
        # 测试用例：类型检测
        test_cases = [
            ('CFX/USDT', 'spot'),
            ('CFX-USDT-SWAP', 'swap'),
            ('BTC-USDT-PERP', 'swap'),
            ('BTC-USD-240329', 'futures'),
            ('CFX-USDT', 'spot'),
            ('INVALID', 'unknown'),
        ]
        
        passed = 0
        total = len(test_cases)
        
        for symbol, expected_type in test_cases:
            try:
                result = validator.get_symbol_type(symbol)
                if result == expected_type:
                    print(f"  ✅ {symbol} -> {result}")
                    passed += 1
                else:
                    print(f"  ❌ {symbol} -> {result} (期望: {expected_type})")
            except Exception as e:
                print(f"  ❌ {symbol} 类型检测失败: {e}")
        
        print(f"\n📊 类型检测测试结果: {passed}/{total} 通过")
        return passed == total
        
    except Exception as e:
        print(f"❌ 交易对类型检测测试失败: {e}")
        return False

def test_grid_strategy_with_swap():
    """测试网格策略是否支持SWAP格式"""
    print("\n🔍 测试网格策略支持SWAP格式...")
    
    try:
        from strategies import GridTrading
        from exchange_manager import exchange_manager
        
        # 模拟交易所连接
        class MockExchange:
            def fetch_ticker(self, symbol):
                return {'last': 0.21375}
            
            def create_limit_buy_order(self, symbol, amount, price):
                return {'id': 'test_buy_123', 'status': 'open'}
            
            def create_limit_sell_order(self, symbol, amount, price):
                return {'id': 'test_sell_123', 'status': 'open'}
        
        # 测试创建网格策略
        try:
            strategy = GridTrading(
                exchange=MockExchange(),
                symbol='CFX-USDT-SWAP',  # 使用SWAP格式
                base_price=0.21375,
                grid_spacing=1.0,
                grid_count=10,
                order_amount=100.0
            )
            print("  ✅ 网格策略成功支持 CFX-USDT-SWAP 格式")
            return True
        except Exception as e:
            print(f"  ❌ 网格策略不支持 CFX-USDT-SWAP 格式: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 网格策略测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始交易对格式修复验证测试")
    print("=" * 60)
    
    tests = [
        ("交易对验证", test_symbol_validation),
        ("交易对标准化", test_symbol_normalization),
        ("交易对类型检测", test_symbol_type_detection),
        ("网格策略SWAP支持", test_grid_strategy_with_swap),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ 测试 '{test_name}' 未通过")
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！交易对格式修复成功！")
        print("现在系统支持以下格式:")
        print("• 现货: CFX/USDT, BTC/USDT")
        print("• 永续合约: CFX-USDT-SWAP, BTC-USDT-SWAP")
        print("• 期货合约: BTC-USD-240329")
        print("• 简化格式: CFX-USDT, BTC-USDT")
        print("\n✅ 您可以使用 CFX-USDT-SWAP 启动网格交易策略了！")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
