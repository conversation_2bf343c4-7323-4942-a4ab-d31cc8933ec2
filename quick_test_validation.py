#!/usr/bin/env python3
"""
快速测试验证
验证测试框架和主要功能是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from test_framework import test_framework
        print("✓ 测试框架导入成功")
    except Exception as e:
        print(f"✗ 测试框架导入失败: {e}")
        return False
    
    try:
        from strategies import GridTrading, MovingAverageStrategy, RSIStrategy, VolumeBreakoutStrategy
        print("✓ 策略模块导入成功")
    except Exception as e:
        print(f"✗ 策略模块导入失败: {e}")
        return False
    
    try:
        from exchange_manager import ExchangeManager
        print("✓ 交易所管理器导入成功")
    except Exception as e:
        print(f"✗ 交易所管理器导入失败: {e}")
        return False
    
    try:
        from environment_manager import environment_manager
        print("✓ 环境管理器导入成功")
    except Exception as e:
        print(f"✗ 环境管理器导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        from test_framework import test_framework
        
        # 测试框架基本功能
        mock_exchange = test_framework.create_mock_exchange()
        print("✓ 模拟交易所创建成功")
        
        # 测试API调用
        ticker = mock_exchange.fetch_ticker('CFX/USDT')
        print(f"✓ 价格获取成功: {ticker['last']}")
        
        balance = mock_exchange.fetch_balance()
        print(f"✓ 余额获取成功: USDT={balance['USDT']['free']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_strategy_creation():
    """测试策略创建"""
    print("\n测试策略创建...")
    
    try:
        from test_framework import test_framework
        from strategies import GridTrading, MovingAverageStrategy
        
        mock_exchange = test_framework.create_mock_exchange()
        
        # 测试网格交易策略
        grid_strategy = GridTrading(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            base_price=0.213456,
            grid_spacing=2.0,
            grid_count=10,
            order_amount=100
        )
        print("✓ 网格交易策略创建成功")
        
        # 测试移动平均线策略
        ma_strategy = MovingAverageStrategy(
            exchange=mock_exchange,
            symbol='CFX/USDT',
            short_period=5,
            long_period=20,
            amount=100
        )
        print("✓ 移动平均线策略创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略创建测试失败: {e}")
        return False

def test_environment_management():
    """测试环境管理"""
    print("\n测试环境管理...")
    
    try:
        from environment_manager import environment_manager
        
        # 测试当前模式
        current_mode = environment_manager.get_current_mode()
        print(f"✓ 当前环境模式: {current_mode}")
        
        # 测试模式检查
        is_simulation = environment_manager.is_simulation_mode()
        print(f"✓ 是否模拟模式: {is_simulation}")
        
        # 测试API配置生成
        config = environment_manager.get_api_config('okx', 'test_key', 'test_secret', 'test_pass')
        print(f"✓ API配置生成成功: sandbox={config['sandbox']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 环境管理测试失败: {e}")
        return False

def test_exchange_manager():
    """测试交易所管理器"""
    print("\n测试交易所管理器...")
    
    try:
        from exchange_manager import ExchangeManager
        
        manager = ExchangeManager()
        print("✓ 交易所管理器创建成功")
        
        # 测试支持的交易所
        supported = manager.supported_exchanges
        print(f"✓ 支持的交易所: {list(supported.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ 交易所管理器测试失败: {e}")
        return False

def run_quick_validation():
    """运行快速验证"""
    print("=" * 60)
    print("量化交易系统快速测试验证")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("策略创建", test_strategy_creation),
        ("环境管理", test_environment_management),
        ("交易所管理器", test_exchange_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"🔥 {test_name} 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print("验证结果摘要")
    print(f"{'='*60}")
    print(f"总测试项: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有验证测试通过！系统基本功能正常。")
        print("\n下一步建议:")
        print("1. 运行完整的系统测试")
        print("2. 验证实盘模式的安全机制")
        print("3. 进行压力测试和性能测试")
    else:
        print("⚠️ 部分验证测试失败，请检查系统配置。")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    run_quick_validation()
