#!/usr/bin/env python3
"""
量化交易系统主程序
基于4.txt文件内容开发的完整GUI量化交易系统

支持策略:
• 网格交易策略 (GridTrading)
• 移动平均线策略 (MovingAverageStrategy) 
• RSI反转策略 (RSIStrategy)
• 成交量突破策略 (VolumeBreakoutStrategy)
• 智能网格策略 (SmartGridStrategy)

支持交易所:
• Binance
• OKX  
• Huobi

作者: AI Assistant
版本: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'ccxt',
        'pandas', 
        'numpy',
        'cryptography'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        error_msg = f"缺少以下依赖包:\n{', '.join(missing_packages)}\n\n请运行以下命令安装:\npip install {' '.join(missing_packages)}"
        messagebox.showerror("依赖错误", error_msg)
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'config', 'data']
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"创建目录: {directory}")
            except Exception as e:
                print(f"创建目录失败 {directory}: {e}")

def main():
    """主函数"""
    try:
        # 检查依赖
        if not check_dependencies():
            return
        
        # 创建必要目录
        create_directories()
        
        # 导入主GUI模块
        from main_gui import TradingSystemGUI
        
        # 创建并运行GUI应用
        app = TradingSystemGUI()
        
        print("量化交易系统启动成功")
        print("=" * 50)
        print("系统功能:")
        print("• 支持Binance、OKX、Huobi三大交易所")
        print("• 集成5种量化交易策略")
        print("• 完整的风险管理系统")
        print("• 实时监控和日志记录")
        print("• API密钥加密存储")
        print("=" * 50)
        
        # 运行GUI主循环
        app.run()
        
    except ImportError as e:
        error_msg = f"导入模块失败:\n{str(e)}\n\n请确保所有文件都在同一目录下"
        messagebox.showerror("导入错误", error_msg)
        print(f"导入错误: {e}")
        
    except Exception as e:
        error_msg = f"程序启动失败:\n{str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
        messagebox.showerror("启动错误", error_msg)
        print(f"启动错误: {e}")
        print(traceback.format_exc())

if __name__ == "__main__":
    # 设置异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"未处理的异常:\n{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}"
        print(error_msg)
        
        # 如果GUI还在运行，显示错误对话框
        try:
            messagebox.showerror("系统错误", error_msg)
        except:
            pass
    
    sys.excepthook = handle_exception
    
    # 运行主程序
    main()
