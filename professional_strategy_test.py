#!/usr/bin/env python3
"""
专业级量化交易系统策略测试
资深Python+加密货币量化专业测试人员
目标：确保100%通过率，所有策略无bug，实盘交易安全
"""

import sys
import traceback
import tkinter as tk
from tkinter import ttk
import time
from datetime import datetime

class ProfessionalStrategyTester:
    """专业策略测试器"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
        self.passed_tests = []
        self.bugs_found = []
        
    def log_test(self, test_name, status, details=""):
        """记录测试结果"""
        result = {
            'name': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        
        if status == "PASS":
            self.passed_tests.append(result)
            print(f"✅ PASS: {test_name}")
        else:
            self.failed_tests.append(result)
            print(f"❌ FAIL: {test_name}")
            if details:
                print(f"   详情: {details}")
                
    def log_bug(self, bug_name, reproduce_steps, expected, actual):
        """记录发现的Bug"""
        bug = {
            'name': bug_name,
            'reproduce_steps': reproduce_steps,
            'expected_result': expected,
            'actual_result': actual,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }
        self.bugs_found.append(bug)
        print(f"🐛 BUG发现: {bug_name}")
        print(f"   重现步骤: {reproduce_steps}")
        print(f"   期望结果: {expected}")
        print(f"   实际结果: {actual}")

    def test_critical_imports(self):
        """测试1: 关键模块导入 - 实盘交易必须"""
        print("\n🔍 测试1: 关键模块导入测试")
        
        critical_modules = [
            ('main_gui', '主界面模块'),
            ('strategies', '策略引擎模块'),
            ('strategy_tabs', '策略标签页模块'),
            ('exchange_manager', '交易所管理模块'),
            ('environment_manager', '环境管理模块'),
            ('risk_manager', '风险管理模块'),
            ('parameter_validator', '参数验证模块'),
            ('user_friendly_input', '用户输入组件'),
            ('config', '配置管理模块')
        ]
        
        for module_name, description in critical_modules:
            try:
                exec(f"import {module_name}")
                self.log_test(f"导入{description}", "PASS")
            except ImportError as e:
                self.log_test(f"导入{description}", "FAIL", f"ImportError: {e}")
                self.log_bug(
                    f"{module_name}模块导入失败",
                    f"执行 import {module_name}",
                    "成功导入模块",
                    f"ImportError: {e}"
                )
            except Exception as e:
                self.log_test(f"导入{description}", "FAIL", f"Exception: {e}")

    def test_strategy_tab_instantiation(self):
        """测试2: 策略标签页实例化 - 确保所有策略可创建"""
        print("\n🔍 测试2: 策略标签页实例化测试")
        
        try:
            import strategy_tabs
            
            root = tk.Tk()
            root.withdraw()
            
            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
                    self.config_manager = None
            
            mock_app = MockMainApp()
            
            strategy_configs = [
                ('GridTradingTab', '网格交易策略'),
                ('MovingAverageTab', '移动平均线策略'),
                ('RSIStrategyTab', 'RSI反转策略'),
                ('FMACDTab', 'FMACD策略'),
                ('AwesomeOscillatorTab', 'AO指标策略'),
                ('VolumeBreakoutTab', '成交量突破策略'),
                ('SmartGridTab', '智能网格策略')
            ]
            
            for class_name, description in strategy_configs:
                try:
                    strategy_class = getattr(strategy_tabs, class_name)
                    strategy_instance = strategy_class(root, mock_app)
                    
                    # 验证关键属性
                    if hasattr(strategy_instance, 'frame'):
                        self.log_test(f"创建{description}", "PASS")
                    else:
                        self.log_test(f"创建{description}", "FAIL", "缺少frame属性")
                        self.log_bug(
                            f"{class_name}缺少frame属性",
                            f"创建{class_name}实例并检查frame属性",
                            "实例应有frame属性",
                            "frame属性不存在"
                        )
                        
                except Exception as e:
                    self.log_test(f"创建{description}", "FAIL", str(e))
                    self.log_bug(
                        f"{class_name}实例化失败",
                        f"执行 {class_name}(root, mock_app)",
                        "成功创建策略实例",
                        f"Exception: {e}"
                    )
            
            root.destroy()
            
        except Exception as e:
            self.log_test("策略标签页模块导入", "FAIL", str(e))

    def test_parameter_preset_functionality(self):
        """测试3: 参数预设功能 - 实盘交易风险控制关键"""
        print("\n🔍 测试3: 参数预设功能测试")
        
        try:
            import strategy_tabs
            
            root = tk.Tk()
            root.withdraw()
            
            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
            
            mock_app = MockMainApp()
            
            # 测试每个策略的参数预设
            strategy_tests = [
                ('GridTradingTab', ['grid_spacing', 'grid_count', 'order_amount']),
                ('MovingAverageTab', ['short_period', 'long_period']),
                ('RSIStrategyTab', ['period', 'oversold', 'overbought']),
                ('FMACDTab', ['fast_period', 'slow_period', 'signal_period', 'position_size']),
                ('AwesomeOscillatorTab', ['short_period', 'long_period']),
                ('VolumeBreakoutTab', ['volume_threshold', 'price_threshold']),
                ('SmartGridTab', ['grid_spacing', 'grid_count', 'order_amount'])
            ]
            
            for class_name, critical_params in strategy_tests:
                try:
                    strategy_class = getattr(strategy_tabs, class_name)
                    strategy_instance = strategy_class(root, mock_app)
                    
                    # 测试预设配置完整性
                    presets = strategy_instance.get_parameter_presets()
                    required_modes = ['novice', 'professional', 'default']
                    
                    for mode in required_modes:
                        if mode in presets:
                            self.log_test(f"{class_name} {mode}模式预设", "PASS")
                        else:
                            self.log_test(f"{class_name} {mode}模式预设", "FAIL")
                            self.log_bug(
                                f"{class_name}缺少{mode}模式预设",
                                f"调用{class_name}.get_parameter_presets()检查{mode}",
                                f"预设字典应包含{mode}键",
                                f"{mode}键不存在于预设字典中"
                            )
                    
                    # 测试参数获取功能
                    for param in critical_params:
                        try:
                            value = strategy_instance.get_current_parameter_value(param)
                            if value != "未知":
                                self.log_test(f"{class_name} 获取{param}参数", "PASS")
                            else:
                                self.log_test(f"{class_name} 获取{param}参数", "FAIL")
                                self.log_bug(
                                    f"{class_name}无法获取{param}参数",
                                    f"调用get_current_parameter_value('{param}')",
                                    "返回有效参数值",
                                    "返回'未知'"
                                )
                        except Exception as e:
                            self.log_test(f"{class_name} 获取{param}参数", "FAIL", str(e))
                    
                    # 测试参数设置功能 - 实盘交易安全关键
                    try:
                        test_config = {critical_params[0]: "999"} if critical_params else {}
                        strategy_instance.set_parameters(test_config)
                        self.log_test(f"{class_name} 参数设置功能", "PASS")
                    except Exception as e:
                        self.log_test(f"{class_name} 参数设置功能", "FAIL", str(e))
                        self.log_bug(
                            f"{class_name}参数设置功能异常",
                            f"调用set_parameters({test_config})",
                            "成功设置参数",
                            f"Exception: {e}"
                        )
                        
                except Exception as e:
                    self.log_test(f"{class_name} 预设功能", "FAIL", str(e))
            
            root.destroy()
            
        except Exception as e:
            self.log_test("参数预设功能", "FAIL", str(e))

    def test_strategy_control_methods(self):
        """测试4: 策略控制方法 - 实盘交易启停安全"""
        print("\n🔍 测试4: 策略控制方法测试")
        
        try:
            import strategy_tabs

            root = tk.Tk()
            root.withdraw()

            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
                    self.config_manager = None

            mock_app = MockMainApp()

            strategy_classes = [
                'GridTradingTab', 'MovingAverageTab', 'RSIStrategyTab',
                'FMACDTab', 'AwesomeOscillatorTab', 'VolumeBreakoutTab', 'SmartGridTab'
            ]

            # 实盘交易必须的控制方法
            critical_methods = [
                ('start_strategy', '启动策略'),
                ('stop_strategy', '停止策略'),
                ('save_config', '保存配置'),
                ('load_config', '加载配置'),
                ('run_backtest', '运行回测'),
                ('add_status_message', '状态消息')
            ]

            for class_name in strategy_classes:
                try:
                    strategy_class = getattr(strategy_tabs, class_name)
                    strategy_instance = strategy_class(root, mock_app)
                    
                    for method_name, description in critical_methods:
                        if hasattr(strategy_instance, method_name):
                            self.log_test(f"{class_name} {description}方法", "PASS")
                        else:
                            self.log_test(f"{class_name} {description}方法", "FAIL")
                            self.log_bug(
                                f"{class_name}缺少{description}方法",
                                f"检查{class_name}实例是否有{method_name}方法",
                                f"应该有{method_name}方法",
                                f"{method_name}方法不存在"
                            )
                    
                    # 测试状态消息功能
                    if hasattr(strategy_instance, 'add_status_message'):
                        try:
                            strategy_instance.add_status_message("测试消息")
                            self.log_test(f"{class_name} 状态消息功能", "PASS")
                        except Exception as e:
                            self.log_test(f"{class_name} 状态消息功能", "FAIL", str(e))
                            self.log_bug(
                                f"{class_name}状态消息功能异常",
                                "调用add_status_message('测试消息')",
                                "成功添加状态消息",
                                f"Exception: {e}"
                            )
                        
                except Exception as e:
                    self.log_test(f"{class_name} 控制方法", "FAIL", str(e))
            
            root.destroy()
            
        except Exception as e:
            self.log_test("策略控制方法", "FAIL", str(e))

    def run_comprehensive_test(self):
        """运行全面测试"""
        start_time = time.time()
        print("🚀 开始专业级量化交易系统策略测试")
        print("=" * 60)
        print("目标：确保100%通过率，所有策略无bug，实盘交易安全")
        print("=" * 60)
        
        # 执行所有测试
        test_methods = [
            self.test_critical_imports,
            self.test_strategy_tab_instantiation,
            self.test_parameter_preset_functionality,
            self.test_strategy_control_methods
        ]
        
        for test_method in test_methods:
            try:
                test_method()
            except Exception as e:
                print(f"❌ 测试方法 {test_method.__name__} 执行失败: {e}")
                traceback.print_exc()
        
        # 生成测试报告
        return self.generate_final_report(start_time)

    def generate_final_report(self, start_time):
        """生成最终测试报告"""
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 专业测试结果统计")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len(self.passed_tests)
        failed_tests = len(self.failed_tests)
        bugs_count = len(self.bugs_found)
        
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"总测试用例: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"发现Bug: {bugs_count}")
        print(f"通过率: {pass_rate:.1f}%")
        print(f"测试耗时: {duration:.2f}秒")
        
        # 返回测试结果
        return {
            'pass_rate': pass_rate,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'bugs_found': bugs_count,
            'failed_test_details': self.failed_tests,
            'bug_details': self.bugs_found
        }

def main():
    """主函数"""
    tester = ProfessionalStrategyTester()
    results = tester.run_comprehensive_test()
    
    if results['pass_rate'] == 100.0:
        print("\n🎉 所有测试通过！系统可以安全投入实盘交易使用。")
        return True, results
    else:
        print(f"\n⚠️ 测试通过率 {results['pass_rate']:.1f}%，发现 {results['bugs_found']} 个Bug")
        print("需要修复所有问题后才能投入实盘交易使用。")
        return False, results

if __name__ == "__main__":
    success, test_results = main()
    sys.exit(0 if success else 1)
