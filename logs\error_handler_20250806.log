2025-08-06 00:27:34 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-06 00:27:34 - ERROR - 错误代码: SYSTEM_001
2025-08-06 00:27:34 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-06 00:27:34 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-06 00:27:34 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-06 00:47:07 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-06 00:47:07 - ERROR - 错误代码: SYSTEM_001
2025-08-06 00:47:07 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-06 00:47:07 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-06 00:47:07 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-06 01:05:44 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-06 01:05:44 - ERROR - 错误代码: SYSTEM_001
2025-08-06 01:05:44 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-06 01:05:44 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-06 01:05:44 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

2025-08-06 01:30:00 - ERROR - 错误处理: 系统错误 - 系统发生未知错误
2025-08-06 01:30:00 - ERROR - 错误代码: SYSTEM_001
2025-08-06 01:30:00 - ERROR - 原始错误: 交易对格式不正确: INVALID_SYMBOL
2025-08-06 01:30:00 - ERROR - 错误上下文: {'strategy': 'GridTrading', 'symbol': 'INVALID_SYMBOL', 'base_price': -1, 'grid_spacing': 0, 'grid_count': 0, 'order_amount': -100}
2025-08-06 01:30:00 - DEBUG - 堆栈跟踪: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 115, in __init__
    self._validate_parameters(symbol, base_price, grid_spacing, grid_count, order_amount)
  File "C:\Users\<USER>\Desktop\yl\2.0\strategies.py", line 176, in _validate_parameters
    validator.validate_symbol(symbol)
  File "C:\Users\<USER>\Desktop\yl\2.0\parameter_validator.py", line 193, in validate_symbol
    raise ValidationError(
parameter_validator.ValidationError: 交易对格式不正确: INVALID_SYMBOL

