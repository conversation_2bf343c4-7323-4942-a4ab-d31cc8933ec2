#!/usr/bin/env python3
"""
策略标签页功能补全验证测试
验证所有缺失功能的补全情况
"""

import sys
import traceback
import tkinter as tk
from tkinter import ttk

def test_volume_breakout_presets():
    """测试成交量突破策略参数预设功能"""
    print("🔍 测试1: 成交量突破策略参数预设功能")
    try:
        from strategy_tabs import VolumeBreakoutTab
        
        root = tk.Tk()
        root.withdraw()
        
        class MockMainApp:
            def __init__(self):
                self.strategies = {}
                self.strategy_threads = {}
        
        mock_app = MockMainApp()
        volume_tab = VolumeBreakoutTab(root, mock_app)
        
        # 测试预设配置
        presets = volume_tab.get_parameter_presets()
        print(f"✅ 预设配置: {list(presets.keys())}")
        
        # 测试参数获取
        for param in ['volume_threshold', 'price_threshold', 'lookback_period']:
            value = volume_tab.get_current_parameter_value(param)
            print(f"✅ 参数获取 {param}: {value}")
        
        # 测试参数设置
        test_config = {'volume_threshold': '2.5', 'price_threshold': '1.8'}
        volume_tab.set_parameters(test_config)
        print("✅ 参数设置功能正常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_smart_grid_presets():
    """测试智能网格策略参数预设功能"""
    print("\n🔍 测试2: 智能网格策略参数预设功能")
    try:
        from strategy_tabs import SmartGridTab
        
        root = tk.Tk()
        root.withdraw()
        
        class MockMainApp:
            def __init__(self):
                self.strategies = {}
                self.strategy_threads = {}
        
        mock_app = MockMainApp()
        smart_grid_tab = SmartGridTab(root, mock_app)
        
        # 测试预设配置
        presets = smart_grid_tab.get_parameter_presets()
        print(f"✅ 预设配置: {list(presets.keys())}")
        
        # 测试参数获取
        for param in ['grid_spacing', 'grid_count', 'order_amount']:
            value = smart_grid_tab.get_current_parameter_value(param)
            print(f"✅ 参数获取 {param}: {value}")
        
        # 测试参数设置
        test_config = {'grid_spacing': '2.0', 'grid_count': '15'}
        smart_grid_tab.set_parameters(test_config)
        print("✅ 参数设置功能正常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_grid_trading_backtest():
    """测试网格交易回测功能"""
    print("\n🔍 测试3: 网格交易回测功能")
    try:
        from strategy_tabs import GridTradingTab
        
        root = tk.Tk()
        root.withdraw()
        
        class MockMainApp:
            def __init__(self):
                self.strategies = {}
                self.strategy_threads = {}
        
        mock_app = MockMainApp()
        grid_tab = GridTradingTab(root, mock_app)
        
        # 检查是否有run_backtest方法
        if hasattr(grid_tab, 'run_backtest'):
            print("✅ 回测方法存在")
        else:
            print("❌ 回测方法不存在")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_buttons():
    """测试配置管理按钮"""
    print("\n🔍 测试4: 配置管理按钮测试")
    
    strategy_classes = [
        ('MovingAverageTab', '移动平均线'),
        ('RSIStrategyTab', 'RSI策略'),
        ('VolumeBreakoutTab', '成交量突破'),
        ('SmartGridTab', '智能网格')
    ]
    
    passed = 0
    total = len(strategy_classes)
    
    for class_name, display_name in strategy_classes:
        try:
            exec(f"from strategy_tabs import {class_name}")
            
            root = tk.Tk()
            root.withdraw()
            
            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
            
            mock_app = MockMainApp()
            strategy_class = eval(class_name)
            strategy_tab = strategy_class(root, mock_app)
            
            # 检查是否有配置管理方法
            has_save = hasattr(strategy_tab, 'save_config')
            has_load = hasattr(strategy_tab, 'load_config')
            has_backtest = hasattr(strategy_tab, 'run_backtest')
            
            print(f"✅ {display_name}: 保存配置={has_save}, 加载配置={has_load}, 回测={has_backtest}")
            
            if has_save and has_load and has_backtest:
                passed += 1
            
            root.destroy()
            
        except Exception as e:
            print(f"❌ {display_name}测试失败: {e}")
    
    print(f"📊 配置管理功能测试: {passed}/{total} 通过")
    return passed == total

def test_preset_buttons_integration():
    """测试参数预设按钮集成"""
    print("\n🔍 测试5: 参数预设按钮集成测试")
    
    strategy_classes = [
        'VolumeBreakoutTab',
        'SmartGridTab'
    ]
    
    passed = 0
    total = len(strategy_classes)
    
    for class_name in strategy_classes:
        try:
            exec(f"from strategy_tabs import {class_name}")
            
            root = tk.Tk()
            root.title(f"{class_name} 预设按钮测试")
            root.geometry("800x600")
            
            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
            
            mock_app = MockMainApp()
            strategy_class = eval(class_name)
            strategy_tab = strategy_class(root, mock_app)
            strategy_tab.frame.pack(fill=tk.BOTH, expand=True)
            
            print(f"✅ {class_name}: 界面创建成功，包含预设按钮")
            
            # 快速显示后关闭
            root.after(1000, root.destroy)
            root.mainloop()
            
            passed += 1
            
        except Exception as e:
            print(f"❌ {class_name}界面测试失败: {e}")
    
    print(f"📊 预设按钮集成测试: {passed}/{total} 通过")
    return passed == total

def test_functionality_completeness():
    """测试功能完整性"""
    print("\n🔍 测试6: 功能完整性检查")
    
    # 定义期望的功能
    expected_features = {
        'GridTradingTab': ['get_parameter_presets', 'run_backtest', 'save_config', 'load_config'],
        'MovingAverageTab': ['get_parameter_presets', 'run_backtest', 'save_config', 'load_config'],
        'RSIStrategyTab': ['get_parameter_presets', 'run_backtest', 'save_config', 'load_config'],
        'FMACDTab': ['get_parameter_presets', 'run_backtest', 'save_config', 'load_config'],
        'AwesomeOscillatorTab': ['get_parameter_presets', 'run_backtest', 'save_config', 'load_config'],
        'VolumeBreakoutTab': ['get_parameter_presets', 'get_current_parameter_value', 'set_parameters', 'run_backtest'],
        'SmartGridTab': ['get_parameter_presets', 'get_current_parameter_value', 'set_parameters', 'run_backtest']
    }
    
    total_features = 0
    passed_features = 0
    
    for class_name, features in expected_features.items():
        try:
            exec(f"from strategy_tabs import {class_name}")
            strategy_class = eval(class_name)
            
            # 创建临时实例检查方法
            root = tk.Tk()
            root.withdraw()
            
            class MockMainApp:
                def __init__(self):
                    self.strategies = {}
                    self.strategy_threads = {}
            
            mock_app = MockMainApp()
            strategy_tab = strategy_class(root, mock_app)
            
            class_passed = 0
            for feature in features:
                total_features += 1
                if hasattr(strategy_tab, feature):
                    passed_features += 1
                    class_passed += 1
                    
            print(f"✅ {class_name}: {class_passed}/{len(features)} 功能完整")
            root.destroy()
            
        except Exception as e:
            print(f"❌ {class_name}检查失败: {e}")
    
    print(f"📊 总体功能完整性: {passed_features}/{total_features} ({passed_features/total_features*100:.1f}%)")
    return passed_features / total_features >= 0.9  # 90%以上通过

def main():
    """主测试函数"""
    print("🚀 开始策略标签页功能补全验证测试")
    print("=" * 60)
    
    tests = [
        test_volume_breakout_presets,
        test_smart_grid_presets,
        test_grid_trading_backtest,
        test_config_buttons,
        test_preset_buttons_integration,
        test_functionality_completeness
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能补全验证测试通过！")
        print("\n✅ 补全的功能包括：")
        print("   🔧 VolumeBreakoutTab参数预设功能实现")
        print("   🔧 SmartGridTab参数预设功能实现")
        print("   📊 GridTradingTab回测功能添加")
        print("   💾 MovingAverageTab和RSIStrategyTab加载配置功能")
        print("   🎛️ 所有策略的参数预设按钮集成")
        print("   📈 VolumeBreakoutTab和SmartGridTab回测功能")
        return True
    else:
        print(f"⚠️ 有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
