#!/usr/bin/env python3
"""
连接监控GUI界面
显示交易所连接状态并提供重连功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime

class ConnectionMonitorGUI:
    """连接监控GUI界面"""
    
    def __init__(self, parent, exchange_manager):
        self.parent = parent
        self.exchange_manager = exchange_manager
        self.monitoring = False
        self.monitor_thread = None
        
        self.create_widgets()
        self.start_monitoring()
    
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        self.frame = ttk.LabelFrame(self.parent, text="连接监控")
        self.frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 控制按钮框架
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(control_frame, text="检查连接", command=self.check_all_connections).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="重连所有", command=self.reconnect_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="刷新状态", command=self.refresh_status).pack(side=tk.LEFT, padx=5)
        
        # 自动重连选项
        self.auto_reconnect_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="自动重连", variable=self.auto_reconnect_var).pack(side=tk.LEFT, padx=10)
        
        # 连接状态表格
        status_frame = ttk.LabelFrame(self.frame, text="连接状态")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格
        columns = ('交易所', '状态', '最后连接', '延迟', '操作')
        self.status_tree = ttk.Treeview(status_frame, columns=columns, show='headings', height=6)
        
        # 设置列标题和宽度
        for col in columns:
            self.status_tree.heading(col, text=col)
            if col == '交易所':
                self.status_tree.column(col, width=100)
            elif col == '状态':
                self.status_tree.column(col, width=80)
            elif col == '最后连接':
                self.status_tree.column(col, width=150)
            elif col == '延迟':
                self.status_tree.column(col, width=80)
            else:
                self.status_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_tree.yview)
        self.status_tree.configure(yscrollcommand=scrollbar.set)
        
        self.status_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.status_tree.bind('<Double-1>', self.on_double_click)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(self.frame, text="连接日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(log_frame, height=8, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 状态栏
        self.status_var = tk.StringVar(value="连接监控已启动")
        status_label = ttk.Label(self.frame, textvariable=self.status_var)
        status_label.pack(fill=tk.X, padx=5, pady=2)
    
    def start_monitoring(self):
        """启动监控"""
        self.monitoring = True
        
        def monitor_loop():
            while self.monitoring:
                try:
                    self.refresh_status()
                    
                    # 如果启用自动重连，检查并重连失效连接
                    if self.auto_reconnect_var.get():
                        self.auto_reconnect_check()
                    
                    time.sleep(30)  # 每30秒更新一次
                except Exception as e:
                    self.add_log(f"监控错误: {e}", "ERROR")
                    time.sleep(60)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.add_log("连接监控已启动", "INFO")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.add_log("连接监控已停止", "INFO")
    
    def refresh_status(self):
        """刷新连接状态"""
        try:
            # 清空现有数据
            for item in self.status_tree.get_children():
                self.status_tree.delete(item)
            
            # 获取所有交易所状态
            exchanges = self.exchange_manager.exchanges
            
            if not exchanges:
                self.status_tree.insert('', tk.END, values=('无连接', '未连接', '-', '-', '-'))
                self.status_var.set("当前无活跃连接")
                return
            
            healthy_count = 0
            total_count = len(exchanges)
            
            for exchange_name, exchange in exchanges.items():
                try:
                    # 测试连接并计算延迟
                    start_time = time.time()
                    
                    test_symbol = self.exchange_manager.supported_exchanges[exchange_name]['test_symbol']
                    ticker = exchange.fetch_ticker(test_symbol)
                    
                    latency = (time.time() - start_time) * 1000  # 转换为毫秒
                    
                    if ticker and 'last' in ticker:
                        status = "✅ 正常"
                        healthy_count += 1
                        last_connect = datetime.now().strftime("%H:%M:%S")
                    else:
                        status = "⚠️ 异常"
                        last_connect = "数据异常"
                    
                    self.status_tree.insert('', tk.END, values=(
                        exchange_name.upper(),
                        status,
                        last_connect,
                        f"{latency:.0f}ms",
                        "重连"
                    ))
                    
                except Exception as e:
                    self.status_tree.insert('', tk.END, values=(
                        exchange_name.upper(),
                        "❌ 失败",
                        "连接失败",
                        "-",
                        "重连"
                    ))
                    self.add_log(f"{exchange_name} 连接检查失败: {e}", "WARNING")
            
            # 更新状态栏
            self.status_var.set(f"连接状态: {healthy_count}/{total_count} 正常")
            
        except Exception as e:
            self.add_log(f"刷新状态失败: {e}", "ERROR")
    
    def check_all_connections(self):
        """检查所有连接"""
        self.add_log("开始检查所有连接...", "INFO")
        
        def check_thread():
            try:
                result = self.exchange_manager.check_and_reconnect_all(max_retries=1)
                
                if result['reconnected']:
                    self.add_log(f"重连成功: {', '.join(result['reconnected'])}", "INFO")
                
                if result['failed']:
                    self.add_log(f"连接失败: {', '.join(result['failed'])}", "ERROR")
                
                if not result['reconnected'] and not result['failed']:
                    self.add_log("所有连接正常", "INFO")
                
                # 刷新显示
                self.refresh_status()
                
            except Exception as e:
                self.add_log(f"连接检查失败: {e}", "ERROR")
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def reconnect_all(self):
        """重连所有交易所"""
        self.add_log("开始重连所有交易所...", "INFO")
        
        def reconnect_thread():
            try:
                exchanges = list(self.exchange_manager.exchanges.keys())
                
                for exchange_name in exchanges:
                    self.add_log(f"重连 {exchange_name}...", "INFO")
                    
                    if self.exchange_manager.reconnect_exchange(exchange_name, max_retries=3):
                        self.add_log(f"{exchange_name} 重连成功", "INFO")
                    else:
                        self.add_log(f"{exchange_name} 重连失败", "ERROR")
                
                # 刷新显示
                self.refresh_status()
                
            except Exception as e:
                self.add_log(f"重连失败: {e}", "ERROR")
        
        threading.Thread(target=reconnect_thread, daemon=True).start()
    
    def auto_reconnect_check(self):
        """自动重连检查"""
        try:
            result = self.exchange_manager.check_and_reconnect_all(max_retries=2)
            
            if result['reconnected']:
                self.add_log(f"自动重连成功: {', '.join(result['reconnected'])}", "INFO")
            
        except Exception as e:
            self.add_log(f"自动重连检查失败: {e}", "WARNING")
    
    def on_double_click(self, event):
        """双击事件处理"""
        item = self.status_tree.selection()[0]
        values = self.status_tree.item(item, 'values')
        
        if len(values) >= 1:
            exchange_name = values[0].lower()
            
            if exchange_name in self.exchange_manager.exchanges:
                # 确认重连
                if messagebox.askyesno("确认重连", f"是否重连 {exchange_name.upper()}？"):
                    self.reconnect_single(exchange_name)
    
    def reconnect_single(self, exchange_name):
        """重连单个交易所"""
        self.add_log(f"重连 {exchange_name}...", "INFO")
        
        def reconnect_thread():
            try:
                if self.exchange_manager.reconnect_exchange(exchange_name, max_retries=3):
                    self.add_log(f"{exchange_name} 重连成功", "INFO")
                else:
                    self.add_log(f"{exchange_name} 重连失败", "ERROR")
                
                # 刷新显示
                self.refresh_status()
                
            except Exception as e:
                self.add_log(f"{exchange_name} 重连失败: {e}", "ERROR")
        
        threading.Thread(target=reconnect_thread, daemon=True).start()
    
    def add_log(self, message, level="INFO"):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        if level == "ERROR":
            color = "red"
        elif level == "WARNING":
            color = "orange"
        else:
            color = "black"
        
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete("1.0", "10.0")
            self.log_text.config(state=tk.DISABLED)

# 使用示例：
# root = tk.Tk()
# monitor = ConnectionMonitorGUI(root, exchange_manager)
# root.mainloop()
