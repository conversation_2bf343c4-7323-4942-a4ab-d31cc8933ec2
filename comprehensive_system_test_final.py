#!/usr/bin/env python3
"""
量化交易系统全面测试框架
资深Python测试人员专用 - 确保100%通过率

测试覆盖：
1. 所有策略标签页功能
2. 完整交易流程
3. 实盘模式安全性
4. 错误处理机制
5. 用户界面交互
"""

import sys
import os
import time
import threading
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import tkinter as tk
from tkinter import ttk

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SystemTestFramework:
    """系统测试框架主类"""
    
    def __init__(self):
        self.test_results = []
        self.failed_tests = []
        self.passed_tests = []
        self.total_tests = 0
        self.start_time = None
        
    def log_test_result(self, test_name, passed, details="", error_msg=""):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'details': details,
            'error_msg': error_msg,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.test_results.append(result)
        if passed:
            self.passed_tests.append(result)
            print(f"✅ PASS: {test_name}")
        else:
            self.failed_tests.append(result)
            print(f"❌ FAIL: {test_name} - {error_msg}")
    
    def run_all_tests(self):
        """运行所有测试"""
        self.start_time = datetime.now()
        print("🚀 开始量化交易系统全面测试")
        print("=" * 80)
        
        # 测试模块导入
        self.test_module_imports()
        
        # 测试核心组件
        self.test_core_components()
        
        # 测试策略类
        self.test_strategy_classes()
        
        # 测试GUI组件
        self.test_gui_components()
        
        # 测试交易流程
        self.test_trading_flows()

        # 测试策略功能
        self.test_strategy_functions()

        # 测试环境切换
        self.test_environment_switching()
        
        # 测试实盘模式安全性
        self.test_live_mode_safety()
        
        # 测试错误处理
        self.test_error_handling()
        
        # 测试用户友好功能
        self.test_user_friendly_features()
        
        # 生成测试报告
        self.generate_test_report()
    
    def test_module_imports(self):
        """测试模块导入"""
        print("\n📦 测试模块导入...")
        
        modules_to_test = [
            'main_gui', 'strategies', 'strategy_tabs', 'exchange_manager',
            'environment_manager', 'risk_manager', 'parameter_validator',
            'error_handler', 'logger', 'user_friendly_input', 'config'
        ]
        
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                self.log_test_result(f"导入模块 {module_name}", True, "模块导入成功")
            except Exception as e:
                self.log_test_result(f"导入模块 {module_name}", False, "", str(e))
    
    def test_core_components(self):
        """测试核心组件"""
        print("\n🔧 测试核心组件...")
        
        # 测试配置管理器
        try:
            from config import ConfigManager, DEFAULT_CONFIG
            config_manager = ConfigManager()
            assert hasattr(config_manager, 'save_strategy_config')
            assert hasattr(config_manager, 'get_strategy_config')
            self.log_test_result("配置管理器初始化", True, "ConfigManager功能正常")
        except Exception as e:
            self.log_test_result("配置管理器初始化", False, "", str(e))
        
        # 测试交易所管理器
        try:
            from exchange_manager import exchange_manager
            assert hasattr(exchange_manager, 'connect_exchange')
            assert hasattr(exchange_manager, 'get_exchange')
            assert hasattr(exchange_manager, 'supported_exchanges')
            self.log_test_result("交易所管理器初始化", True, "ExchangeManager功能正常")
        except Exception as e:
            self.log_test_result("交易所管理器初始化", False, "", str(e))
        
        # 测试环境管理器
        try:
            from environment_manager import environment_manager
            assert hasattr(environment_manager, 'switch_to_simulation')
            assert hasattr(environment_manager, 'switch_to_testnet')
            assert hasattr(environment_manager, 'switch_to_live')
            assert environment_manager.current_mode == 'simulation'
            self.log_test_result("环境管理器初始化", True, "EnvironmentManager功能正常")
        except Exception as e:
            self.log_test_result("环境管理器初始化", False, "", str(e))
        
        # 测试参数验证器
        try:
            from parameter_validator import validator
            assert hasattr(validator, 'validate_symbol')
            assert hasattr(validator, 'validate_price')
            assert hasattr(validator, 'validate_amount')
            self.log_test_result("参数验证器初始化", True, "ParameterValidator功能正常")
        except Exception as e:
            self.log_test_result("参数验证器初始化", False, "", str(e))
    
    def test_strategy_classes(self):
        """测试策略类"""
        print("\n📈 测试策略类...")
        
        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        mock_exchange.create_limit_buy_order.return_value = {'id': 'test_buy_123', 'status': 'open'}
        mock_exchange.create_limit_sell_order.return_value = {'id': 'test_sell_123', 'status': 'open'}
        
        # 测试网格交易策略
        try:
            from strategies import GridTrading

            # 测试正常初始化
            strategy = GridTrading(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                base_price=0.21375,
                grid_spacing=1.0,
                grid_count=5,
                order_amount=100
            )
            assert hasattr(strategy, 'run')
            assert hasattr(strategy, 'stop')
            assert strategy.symbol == 'CFX/USDT'

            # 测试支持的新格式
            strategy2 = GridTrading(
                exchange=mock_exchange,
                symbol='CFX-USDT-SWAP',
                base_price=0.21375,
                grid_spacing=1.0,
                grid_count=5,
                order_amount=100
            )
            assert strategy2.symbol == 'CFX-USDT-SWAP'

            self.log_test_result("网格交易策略初始化", True, "GridTrading类功能正常，支持多种交易对格式")
        except Exception as e:
            self.log_test_result("网格交易策略初始化", False, "", str(e))
        
        # 测试移动平均线策略
        try:
            from strategies import MovingAverageStrategy
            strategy = MovingAverageStrategy(
                exchange=mock_exchange,
                symbol='BTC/USDT',
                short_period=10,
                long_period=30,
                amount=100
            )
            assert hasattr(strategy, 'run_live')
            assert hasattr(strategy, 'stop')
            self.log_test_result("移动平均线策略初始化", True, "MovingAverageStrategy类功能正常")
        except Exception as e:
            self.log_test_result("移动平均线策略初始化", False, "", str(e))
        
        # 测试RSI策略
        try:
            from strategies import RSIStrategy
            strategy = RSIStrategy(
                exchange=mock_exchange,
                symbol='ETH/USDT',
                period=14,
                oversold=30,
                overbought=70,
                amount=100
            )
            assert hasattr(strategy, 'run_live')
            assert hasattr(strategy, 'stop')
            self.log_test_result("RSI策略初始化", True, "RSIStrategy类功能正常")
        except Exception as e:
            self.log_test_result("RSI策略初始化", False, "", str(e))
    
    def test_gui_components(self):
        """测试GUI组件"""
        print("\n🖥️ 测试GUI组件...")
        
        # 测试用户友好输入组件
        try:
            from user_friendly_input import create_user_friendly_input, PARAMETER_DEFINITIONS
            
            # 检查参数定义
            assert len(PARAMETER_DEFINITIONS) >= 10
            assert 'grid_base_price' in PARAMETER_DEFINITIONS
            assert 'grid_spacing' in PARAMETER_DEFINITIONS
            
            self.log_test_result("用户友好输入组件", True, f"包含{len(PARAMETER_DEFINITIONS)}个参数定义")
        except Exception as e:
            self.log_test_result("用户友好输入组件", False, "", str(e))
        
        # 测试策略标签页类
        try:
            from strategy_tabs import GridTradingTab, MovingAverageTab, RSIStrategyTab
            
            # 创建模拟主应用
            mock_app = Mock()
            mock_app.config_manager = Mock()
            mock_app.strategies = {}
            mock_app.strategy_threads = {}
            
            # 测试网格交易标签页
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            tab = GridTradingTab(root, mock_app)
            assert hasattr(tab, 'create_widgets')
            assert hasattr(tab, 'start_strategy')
            
            root.destroy()
            self.log_test_result("策略标签页类", True, "策略标签页类功能正常")
        except Exception as e:
            self.log_test_result("策略标签页类", False, "", str(e))
    
    def test_trading_flows(self):
        """测试交易流程"""
        print("\n💱 测试交易流程...")
        
        # 测试参数验证流程
        try:
            from parameter_validator import validator
            
            # 测试有效参数
            validator.validate_symbol('CFX/USDT')
            validator.validate_price(0.21375)
            validator.validate_amount(100)
            
            # 测试无效参数
            try:
                validator.validate_symbol('INVALID')
                self.log_test_result("参数验证-无效符号", False, "", "应该抛出异常但没有")
            except:
                pass  # 预期的异常
            
            self.log_test_result("参数验证流程", True, "参数验证功能正常")
        except Exception as e:
            self.log_test_result("参数验证流程", False, "", str(e))

    def test_strategy_functions(self):
        """测试策略功能"""
        print("\n⚙️ 测试策略功能...")

        # 创建模拟交易所
        mock_exchange = Mock()
        mock_exchange.fetch_ticker.return_value = {'last': 0.21375}
        mock_exchange.create_limit_buy_order.return_value = {'id': 'test_buy_123', 'status': 'open'}
        mock_exchange.create_limit_sell_order.return_value = {'id': 'test_sell_123', 'status': 'open'}

        # 测试网格交易策略的核心功能
        try:
            from strategies import GridTrading

            strategy = GridTrading(
                exchange=mock_exchange,
                symbol='CFX/USDT',
                base_price=0.21375,
                grid_spacing=1.0,
                grid_count=3,  # 小网格便于测试
                order_amount=100
            )

            # 测试策略属性
            assert strategy.running == False
            assert strategy.grid_count == 3
            assert strategy.grid_spacing == 1.0

            # 测试网格基本属性
            assert hasattr(strategy, 'orders')

            self.log_test_result("网格交易策略功能", True, "网格交易核心功能正常")
        except Exception as e:
            self.log_test_result("网格交易策略功能", False, "", str(e))

        # 测试参数验证功能
        try:
            from strategies import GridTrading

            # 测试无效参数应该抛出异常
            try:
                invalid_strategy = GridTrading(
                    exchange=mock_exchange,
                    symbol='INVALID_SYMBOL',
                    base_price=-1,  # 无效价格
                    grid_spacing=0,  # 无效间距
                    grid_count=0,   # 无效数量
                    order_amount=-100  # 无效数量
                )
                self.log_test_result("策略参数验证", False, "", "应该抛出异常但没有")
            except:
                # 预期的异常
                self.log_test_result("策略参数验证", True, "参数验证正确拒绝无效参数")

        except Exception as e:
            self.log_test_result("策略参数验证", False, "", str(e))

    def test_environment_switching(self):
        """测试环境切换"""
        print("\n🔄 测试环境切换...")
        
        try:
            from environment_manager import environment_manager
            
            # 测试切换到模拟模式
            result = environment_manager.switch_to_simulation()
            assert result == True
            assert environment_manager.current_mode == 'simulation'
            
            # 测试环境状态
            status = environment_manager.get_status_info()
            assert 'mode' in status
            assert 'mode_name' in status
            
            self.log_test_result("环境切换功能", True, "环境切换功能正常")
        except Exception as e:
            self.log_test_result("环境切换功能", False, "", str(e))
    
    def test_live_mode_safety(self):
        """测试实盘模式安全性"""
        print("\n🛡️ 测试实盘模式安全性...")
        
        try:
            from environment_manager import environment_manager
            
            # 测试数据真实性验证方法存在
            assert hasattr(environment_manager, '_validate_live_mode_data_integrity')
            assert hasattr(environment_manager, '_disable_simulation_data_sources')
            assert hasattr(environment_manager, '_verify_production_api_endpoints')
            
            # 测试交易所管理器的安全功能
            from exchange_manager import exchange_manager
            assert hasattr(exchange_manager, 'validate_live_mode_connections')
            assert hasattr(exchange_manager, 'force_production_mode')
            assert hasattr(exchange_manager, '_is_test_api_key')
            
            self.log_test_result("实盘模式安全性", True, "实盘模式安全机制完整")
        except Exception as e:
            self.log_test_result("实盘模式安全性", False, "", str(e))
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n🚨 测试错误处理...")

        try:
            from error_handler import error_handler, ErrorCode

            # 测试错误代码定义
            assert hasattr(ErrorCode, 'SYSTEM_ERROR')
            assert hasattr(ErrorCode, 'NETWORK_CONNECTION_FAILED')

            # 测试错误处理器
            assert hasattr(error_handler, 'handle_error')

            self.log_test_result("错误处理机制", True, "错误处理机制完整")
        except Exception as e:
            self.log_test_result("错误处理机制", False, "", str(e))

        # 测试用户友好消息（可选）
        try:
            from user_friendly_messages import UserMessageCenter
            message_center = UserMessageCenter()
            assert hasattr(message_center, 'show_success')
            self.log_test_result("用户友好消息", True, "用户友好消息功能正常")
        except Exception as e:
            self.log_test_result("用户友好消息", False, "", str(e))
    
    def test_user_friendly_features(self):
        """测试用户友好功能"""
        print("\n👥 测试用户友好功能...")
        
        try:
            from user_friendly_input import PARAMETER_DEFINITIONS
            
            # 检查参数定义的完整性
            for key, param_info in PARAMETER_DEFINITIONS.items():
                assert hasattr(param_info, 'name')
                assert hasattr(param_info, 'description')
                assert hasattr(param_info, 'suggested_range')
                assert hasattr(param_info, 'examples')
                assert hasattr(param_info, 'risk_warnings')
                
                # 检查内容不为空
                assert param_info.name
                assert param_info.description
                assert param_info.suggested_range
                assert len(param_info.examples) > 0
                assert len(param_info.risk_warnings) > 0
            
            self.log_test_result("用户友好功能", True, f"验证了{len(PARAMETER_DEFINITIONS)}个参数定义")
        except Exception as e:
            self.log_test_result("用户友好功能", False, "", str(e))
    
    def generate_test_report(self):
        """生成测试报告"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        self.total_tests = len(self.test_results)
        pass_rate = (len(self.passed_tests) / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print("\n" + "=" * 80)
        print("📊 测试结果统计")
        print("=" * 80)
        print(f"总测试数: {self.total_tests}")
        print(f"通过测试: {len(self.passed_tests)}")
        print(f"失败测试: {len(self.failed_tests)}")
        print(f"通过率: {pass_rate:.1f}%")
        print(f"测试耗时: {duration.total_seconds():.2f}秒")
        
        if self.failed_tests:
            print(f"\n❌ 失败的测试:")
            for test in self.failed_tests:
                print(f"  - {test['test_name']}: {test['error_msg']}")
        
        # 保存详细报告
        self.save_detailed_report()
        
        return pass_rate == 100.0
    
    def save_detailed_report(self):
        """保存详细测试报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"comprehensive_test_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 量化交易系统全面测试报告\n\n")
            f.write(f"**测试时间**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**总测试数**: {self.total_tests}\n")
            f.write(f"**通过测试**: {len(self.passed_tests)}\n")
            f.write(f"**失败测试**: {len(self.failed_tests)}\n")
            f.write(f"**通过率**: {len(self.passed_tests)/self.total_tests*100:.1f}%\n\n")
            
            f.write("## 详细测试结果\n\n")
            for result in self.test_results:
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                f.write(f"### {status} {result['test_name']}\n")
                f.write(f"- **时间**: {result['timestamp']}\n")
                if result['details']:
                    f.write(f"- **详情**: {result['details']}\n")
                if result['error_msg']:
                    f.write(f"- **错误**: {result['error_msg']}\n")
                f.write("\n")
        
        print(f"\n📋 详细测试报告已保存: {report_file}")

def main():
    """主测试函数"""
    test_framework = SystemTestFramework()
    success = test_framework.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！系统质量达到100%标准！")
        return 0
    else:
        print("\n⚠️ 部分测试失败，需要修复后重新测试。")
        return 1

if __name__ == "__main__":
    exit(main())
