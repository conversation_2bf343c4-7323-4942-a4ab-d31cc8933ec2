#!/usr/bin/env python3
"""
环境控制面板
提供实盘/模拟环境切换的GUI控件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from environment_manager import environment_manager
from logger import get_logger

class EnvironmentControlPanel:
    """环境控制面板"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        self.logger = get_logger("environment_control")
        
        # 控件变量
        self.live_mode_var = tk.BooleanVar(value=False)
        self.testnet_var = tk.BooleanVar(value=False)
        
        # 状态标签
        self.status_label = None
        self.mode_indicator = None
        
        self.create_widgets()
        self.update_display()
        
        # 注册环境变化回调
        environment_manager.add_mode_change_callback(self.on_mode_changed)
        
        self.logger.info("环境控制面板初始化完成")
    
    def create_widgets(self):
        """创建控件"""
        # 主框架
        self.frame = ttk.LabelFrame(self.parent, text="环境控制")
        self.frame.pack(fill=tk.X, padx=5, pady=2)
        
        # 左侧控制区域
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=5)
        
        # 实盘模式复选框
        self.live_mode_cb = ttk.Checkbutton(
            control_frame,
            text="实盘模式",
            variable=self.live_mode_var,
            command=self.on_live_mode_changed
        )
        self.live_mode_cb.pack(side=tk.LEFT, padx=5)
        
        # 测试环境复选框
        self.testnet_cb = ttk.Checkbutton(
            control_frame,
            text="使用测试环境",
            variable=self.testnet_var,
            command=self.on_testnet_changed,
            state=tk.DISABLED  # 默认禁用
        )
        self.testnet_cb.pack(side=tk.LEFT, padx=5)
        
        # 分隔符
        ttk.Separator(control_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 快速切换按钮
        ttk.Button(
            control_frame,
            text="模拟模式",
            command=self.switch_to_simulation
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            control_frame,
            text="测试网",
            command=self.switch_to_testnet
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            control_frame,
            text="实盘",
            command=self.switch_to_live
        ).pack(side=tk.LEFT, padx=2)
        
        # 右侧状态显示区域
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(side=tk.RIGHT, padx=5, pady=5)
        
        # 模式指示器
        self.mode_indicator = ttk.Label(
            status_frame,
            text="🟢 模拟模式",
            font=('Arial', 10, 'bold')
        )
        self.mode_indicator.pack(side=tk.RIGHT, padx=5)
        
        # 状态描述
        self.status_label = ttk.Label(
            status_frame,
            text="使用模拟数据，无真实资金风险",
            font=('Arial', 8)
        )
        self.status_label.pack(side=tk.RIGHT, padx=5)
    
    def on_live_mode_changed(self):
        """实盘模式复选框变化处理"""
        is_live = self.live_mode_var.get()
        
        if is_live:
            # 启用测试环境选项
            self.testnet_cb.config(state=tk.NORMAL)
            
            # 检查是否选择测试环境
            if self.testnet_var.get():
                success = environment_manager.switch_to_testnet()
            else:
                success = environment_manager.switch_to_live()
            
            if not success:
                # 切换失败，恢复状态
                self.live_mode_var.set(False)
                self.testnet_cb.config(state=tk.DISABLED)
                return
        else:
            # 切换到模拟模式
            environment_manager.switch_to_simulation()
            self.testnet_var.set(False)
            self.testnet_cb.config(state=tk.DISABLED)
        
        self.update_display()
    
    def on_testnet_changed(self):
        """测试环境复选框变化处理"""
        if not self.live_mode_var.get():
            # 如果实盘模式未启用，不允许选择测试环境
            self.testnet_var.set(False)
            return
        
        use_testnet = self.testnet_var.get()
        
        if use_testnet:
            success = environment_manager.switch_to_testnet()
        else:
            success = environment_manager.switch_to_live()
        
        if not success:
            # 切换失败，恢复状态
            self.testnet_var.set(not use_testnet)
            return
        
        self.update_display()
    
    def switch_to_simulation(self):
        """切换到模拟模式"""
        environment_manager.switch_to_simulation()
        self.live_mode_var.set(False)
        self.testnet_var.set(False)
        self.testnet_cb.config(state=tk.DISABLED)
        self.update_display()
    
    def switch_to_testnet(self):
        """切换到测试网模式"""
        if environment_manager.switch_to_testnet():
            self.live_mode_var.set(True)
            self.testnet_var.set(True)
            self.testnet_cb.config(state=tk.NORMAL)
            self.update_display()
    
    def switch_to_live(self):
        """切换到实盘模式"""
        if environment_manager.switch_to_live():
            self.live_mode_var.set(True)
            self.testnet_var.set(False)
            self.testnet_cb.config(state=tk.NORMAL)
            self.update_display()
    
    def update_display(self):
        """更新显示状态"""
        status_info = environment_manager.get_status_info()
        
        # 更新模式指示器
        mode_text = environment_manager.get_mode_display_text()
        self.mode_indicator.config(text=mode_text)
        
        # 更新状态描述
        description = status_info['description']
        self.status_label.config(text=description)
        
        # 根据模式设置颜色
        color = status_info['color']
        if color == 'red':
            self.mode_indicator.config(foreground='red')
        elif color == 'orange':
            self.mode_indicator.config(foreground='orange')
        else:
            self.mode_indicator.config(foreground='green')
        
        # 记录状态变化
        self.logger.info(f"环境状态更新: {status_info['mode_name']} - {description}")
    
    def on_mode_changed(self, old_mode, new_mode):
        """环境模式变化回调"""
        self.logger.info(f"环境模式变化: {old_mode} -> {new_mode}")
        
        # 更新控件状态
        if new_mode == environment_manager.MODE_SIMULATION:
            self.live_mode_var.set(False)
            self.testnet_var.set(False)
            self.testnet_cb.config(state=tk.DISABLED)
        elif new_mode == environment_manager.MODE_TESTNET:
            self.live_mode_var.set(True)
            self.testnet_var.set(True)
            self.testnet_cb.config(state=tk.NORMAL)
        elif new_mode == environment_manager.MODE_LIVE:
            self.live_mode_var.set(True)
            self.testnet_var.set(False)
            self.testnet_cb.config(state=tk.NORMAL)
        
        # 更新显示
        self.update_display()
        
        # 通知主应用
        if hasattr(self.main_app, 'on_environment_changed'):
            self.main_app.on_environment_changed(old_mode, new_mode)
    
    def get_current_status(self):
        """获取当前状态信息"""
        return environment_manager.get_status_info()
    
    def show_environment_info(self):
        """显示环境信息对话框"""
        status_info = environment_manager.get_status_info()
        
        info_text = f"""
当前环境信息

模式: {status_info['mode_name']}
描述: {status_info['description']}
使用沙盒: {'是' if status_info['use_sandbox'] else '否'}
使用真实API: {'是' if status_info['use_real_api'] else '否'}
实盘模式: {'是' if status_info['is_live'] else '否'}
风险确认: {'是' if status_info['risk_acknowledged'] else '否'}

更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        messagebox.showinfo("环境信息", info_text)


class EnvironmentStatusBar:
    """环境状态栏"""
    
    def __init__(self, parent):
        self.parent = parent
        self.logger = get_logger("environment_status")
        
        self.create_widgets()
        self.update_status()
        
        # 注册环境变化回调
        environment_manager.add_mode_change_callback(self.on_mode_changed)
        
        # 定期更新状态
        self.start_status_update()
    
    def create_widgets(self):
        """创建状态栏控件"""
        # 环境状态框架
        self.env_frame = ttk.Frame(self.parent)
        self.env_frame.pack(side=tk.LEFT, padx=5)
        
        # 环境模式标签
        self.mode_label = ttk.Label(
            self.env_frame,
            text="🟢 模拟模式",
            font=('Arial', 9, 'bold')
        )
        self.mode_label.pack(side=tk.LEFT)
        
        # 分隔符
        ttk.Separator(self.parent, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
    
    def update_status(self):
        """更新状态显示"""
        status_info = environment_manager.get_status_info()
        
        # 更新模式显示
        mode_text = environment_manager.get_mode_display_text()
        self.mode_label.config(text=mode_text)
        
        # 设置颜色
        color = status_info['color']
        if color == 'red':
            self.mode_label.config(foreground='red')
        elif color == 'orange':
            self.mode_label.config(foreground='orange')
        else:
            self.mode_label.config(foreground='green')
    
    def on_mode_changed(self, old_mode, new_mode):
        """环境模式变化回调"""
        self.update_status()
    
    def start_status_update(self):
        """启动状态更新"""
        def update_loop():
            self.update_status()
            # 每5秒更新一次
            self.parent.after(5000, update_loop)
        
        update_loop()
