#!/usr/bin/env python3
"""
量化交易系统测试脚本
测试各个模块的基本功能
"""

import sys
import traceback

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import config
        print("✓ config 模块导入成功")
    except Exception as e:
        print(f"✗ config 模块导入失败: {e}")
        return False
    
    try:
        import logger
        print("✓ logger 模块导入成功")
    except Exception as e:
        print(f"✗ logger 模块导入失败: {e}")
        return False
    
    try:
        import exchange_manager
        print("✓ exchange_manager 模块导入成功")
    except Exception as e:
        print(f"✗ exchange_manager 模块导入失败: {e}")
        return False
    
    try:
        import risk_manager
        print("✓ risk_manager 模块导入成功")
    except Exception as e:
        print(f"✗ risk_manager 模块导入失败: {e}")
        return False
    
    try:
        import strategies
        print("✓ strategies 模块导入成功")
    except Exception as e:
        print(f"✗ strategies 模块导入失败: {e}")
        return False
    
    try:
        import strategies_extended
        print("✓ strategies_extended 模块导入成功")
    except Exception as e:
        print(f"✗ strategies_extended 模块导入失败: {e}")
        return False
    
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from config import ConfigManager
        config_manager = ConfigManager("test_config.json")
        
        # 测试设置密码
        config_manager.set_password("test_password")
        print("✓ 密码设置成功")
        
        # 测试保存策略配置
        test_config = {
            'symbol': 'BTC/USDT',
            'amount': 0.001
        }
        config_manager.save_strategy_config('test_strategy', test_config)
        print("✓ 策略配置保存成功")
        
        # 测试读取策略配置
        loaded_config = config_manager.get_strategy_config('test_strategy')
        if loaded_config == test_config:
            print("✓ 策略配置读取成功")
        else:
            print("✗ 策略配置读取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_logger():
    """测试日志系统"""
    print("\n测试日志系统...")
    
    try:
        from logger import get_logger, log_trade, log_error
        
        # 获取日志器
        logger = get_logger("test")
        logger.info("测试日志消息")
        print("✓ 日志器创建成功")
        
        # 测试交易日志
        log_trade("test_strategy", "买入", "BTC/USDT", 0.001, 30000.0, "test_order_123")
        print("✓ 交易日志记录成功")
        
        # 测试错误日志
        log_error("test_strategy", "测试错误消息")
        print("✓ 错误日志记录成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        traceback.print_exc()
        return False

def test_exchange_manager():
    """测试交易所管理器"""
    print("\n测试交易所管理器...")
    
    try:
        from exchange_manager import ExchangeManager
        
        manager = ExchangeManager()
        print("✓ 交易所管理器创建成功")
        
        # 测试支持的交易所
        supported = manager.supported_exchanges
        if 'binance' in supported and 'okx' in supported and 'huobi' in supported:
            print("✓ 支持的交易所配置正确")
        else:
            print("✗ 支持的交易所配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 交易所管理器测试失败: {e}")
        traceback.print_exc()
        return False

def test_strategies():
    """测试策略类"""
    print("\n测试策略类...")
    
    try:
        from strategies import GridTrading, MovingAverageStrategy, RSIStrategy
        print("✓ 基础策略类导入成功")
        
        from strategies_extended import VolumeBreakoutStrategy, SmartGridStrategy
        print("✓ 扩展策略类导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略类测试失败: {e}")
        traceback.print_exc()
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n测试GUI组件...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 可用")
        
        # 测试创建窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✓ GUI窗口创建测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("量化交易系统组件测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("日志系统", test_logger),
        ("交易所管理器", test_exchange_manager),
        ("策略类", test_strategies),
        ("GUI组件", test_gui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统组件工作正常。")
        print("\n可以运行主程序:")
        print("python main.py")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    print("=" * 60)
    
    # 清理测试文件
    try:
        import os
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
    except:
        pass

if __name__ == "__main__":
    main()
