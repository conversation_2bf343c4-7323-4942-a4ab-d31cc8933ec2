#!/usr/bin/env python3
"""
修复价格获取问题的诊断和修复脚本
"""

import ccxt
import time
import json
from datetime import datetime

def test_exchange_connection():
    """测试交易所连接和价格获取"""
    print("=" * 60)
    print("交易所价格获取诊断")
    print("=" * 60)
    
    # 测试不同交易所的价格获取
    exchanges_config = {
        'binance': {
            'class': ccxt.binance,
            'symbols': ['BTC/USDT', 'ETH/USDT', 'CFX/USDT']
        },
        'okx': {
            'class': ccxt.okx,
            'symbols': ['BTC/USDT', 'ETH/USDT', 'CFX/USDT']
        },
        'huobi': {
            'class': ccxt.huobi,
            'symbols': ['BTC/USDT', 'ETH/USDT', 'CFX/USDT']
        }
    }
    
    for exchange_name, config in exchanges_config.items():
        print(f"\n测试 {exchange_name.upper()}:")
        print("-" * 30)
        
        try:
            # 创建交易所实例
            exchange = config['class']({
                'enableRateLimit': True,
                'sandbox': False,  # 使用实盘数据
                'timeout': 30000,
            })
            
            # 测试连接
            markets = exchange.load_markets()
            print(f"✓ 连接成功，支持 {len(markets)} 个交易对")
            
            # 测试价格获取
            for symbol in config['symbols']:
                try:
                    if symbol in markets:
                        ticker = exchange.fetch_ticker(symbol)
                        price = ticker['last']
                        print(f"✓ {symbol}: {price}")
                    else:
                        print(f"✗ {symbol}: 交易对不存在")
                except Exception as e:
                    print(f"✗ {symbol}: {str(e)[:50]}...")
            
        except Exception as e:
            print(f"✗ {exchange_name} 连接失败: {str(e)[:50]}...")

def test_cfx_price_specifically():
    """专门测试CFX价格获取"""
    print(f"\n{'='*60}")
    print("CFX价格专项测试")
    print("=" * 60)
    
    # CFX在不同交易所的交易对名称可能不同
    cfx_symbols = [
        'CFX/USDT',
        'CFX-USDT-SWAP',  # OKX合约
        'CFXUSDT',        # 某些交易所格式
        'CFX/USD',
        'CFX/BTC'
    ]
    
    exchanges = [
        ('Binance', ccxt.binance()),
        ('OKX', ccxt.okx()),
        ('Huobi', ccxt.huobi())
    ]
    
    for exchange_name, exchange in exchanges:
        print(f"\n{exchange_name} CFX价格:")
        print("-" * 20)
        
        try:
            exchange.enableRateLimit = True
            exchange.timeout = 30000
            markets = exchange.load_markets()
            
            # 查找CFX相关交易对
            cfx_markets = [symbol for symbol in markets.keys() if 'CFX' in symbol.upper()]
            
            if cfx_markets:
                print(f"找到CFX交易对: {cfx_markets[:5]}")  # 显示前5个
                
                for symbol in cfx_markets[:3]:  # 测试前3个
                    try:
                        ticker = exchange.fetch_ticker(symbol)
                        price = ticker['last']
                        volume = ticker['baseVolume']
                        print(f"✓ {symbol}: ${price:.6f} (24h量: {volume:.0f})")
                    except Exception as e:
                        print(f"✗ {symbol}: {str(e)[:30]}...")
            else:
                print("✗ 未找到CFX交易对")
                
        except Exception as e:
            print(f"✗ {exchange_name} 错误: {str(e)[:50]}...")

def diagnose_price_issue():
    """诊断价格问题"""
    print(f"\n{'='*60}")
    print("价格问题诊断")
    print("=" * 60)
    
    # 检查可能的问题
    issues = []
    
    print("检查常见问题:")
    
    # 1. 网络连接
    try:
        import requests
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=5)
        if response.status_code == 200:
            print("✓ 网络连接正常")
        else:
            print("✗ 网络连接异常")
            issues.append("网络连接问题")
    except Exception as e:
        print(f"✗ 网络测试失败: {e}")
        issues.append("网络连接问题")
    
    # 2. 交易对名称
    print("✓ 检查交易对名称格式")
    print("  - CFX/USDT (标准格式)")
    print("  - CFX-USDT-SWAP (OKX合约)")
    print("  - CFXUSDT (某些交易所)")
    
    # 3. API限制
    print("✓ 检查API调用频率限制")
    print("  - 建议间隔: 1-5秒")
    print("  - 当前间隔: 5秒 (正常)")
    
    # 4. 数据格式
    print("✓ 检查价格数据格式")
    print("  - 应该是浮点数")
    print("  - 当前显示: 0.21 (可能是默认值)")
    
    return issues

def create_price_fix():
    """创建价格修复代码"""
    print(f"\n{'='*60}")
    print("生成价格修复代码")
    print("=" * 60)
    
    fix_code = '''
# 修复价格获取的代码片段
def get_real_price(self, symbol='CFX/USDT'):
    """获取真实价格的改进版本"""
    try:
        # 方法1: 使用ticker
        ticker = self.exchange.fetch_ticker(symbol)
        price = float(ticker['last'])
        
        if price > 0:
            return price
        
        # 方法2: 使用orderbook
        orderbook = self.exchange.fetch_order_book(symbol, limit=5)
        if orderbook['bids'] and orderbook['asks']:
            bid_price = float(orderbook['bids'][0][0])
            ask_price = float(orderbook['asks'][0][0])
            price = (bid_price + ask_price) / 2
            return price
        
        # 方法3: 使用最近交易
        trades = self.exchange.fetch_trades(symbol, limit=1)
        if trades:
            price = float(trades[0]['price'])
            return price
            
        return None
        
    except Exception as e:
        print(f"获取价格失败: {e}")
        return None

# 在网格策略中的使用示例
def update_price_in_grid_strategy():
    """在网格策略中更新价格获取逻辑"""
    
    # 替换原来的价格获取代码
    # 原代码可能是:
    # current_price = 0.21  # 硬编码的默认值
    
    # 新代码:
    current_price = self.get_real_price(self.symbol)
    
    if current_price is None:
        self.logger.error("无法获取当前价格，策略暂停")
        return
    
    if current_price <= 0:
        self.logger.error(f"价格异常: {current_price}")
        return
    
    self.logger.info(f"当前价格: {current_price:.6f}")
    
    # 继续网格策略逻辑...
'''
    
    with open('price_fix_code.py', 'w', encoding='utf-8') as f:
        f.write(fix_code)
    
    print("✓ 已生成修复代码: price_fix_code.py")

def test_specific_symbol():
    """测试特定交易对"""
    print(f"\n{'='*60}")
    print("测试CFX-USDT-SWAP价格")
    print("=" * 60)
    
    try:
        # 测试OKX的CFX合约
        exchange = ccxt.okx({
            'enableRateLimit': True,
            'timeout': 30000,
        })
        
        markets = exchange.load_markets()
        
        # 查找CFX相关的所有交易对
        cfx_symbols = [s for s in markets.keys() if 'CFX' in s]
        print(f"OKX上的CFX交易对: {cfx_symbols}")
        
        # 测试CFX-USDT-SWAP
        if 'CFX-USDT-SWAP' in markets:
            ticker = exchange.fetch_ticker('CFX-USDT-SWAP')
            print(f"CFX-USDT-SWAP 价格: {ticker['last']}")
            print(f"24h变化: {ticker['percentage']}%")
            print(f"24h成交量: {ticker['baseVolume']}")
        else:
            print("CFX-USDT-SWAP 不存在")
            
        # 测试现货CFX/USDT
        if 'CFX/USDT' in markets:
            ticker = exchange.fetch_ticker('CFX/USDT')
            print(f"CFX/USDT 价格: {ticker['last']}")
        else:
            print("CFX/USDT 不存在")
            
    except Exception as e:
        print(f"测试失败: {e}")

def main():
    """主函数"""
    print("量化交易系统价格问题诊断工具")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 基础连接测试
    test_exchange_connection()
    
    # 2. CFX专项测试
    test_cfx_price_specifically()
    
    # 3. 特定交易对测试
    test_specific_symbol()
    
    # 4. 问题诊断
    issues = diagnose_price_issue()
    
    # 5. 生成修复代码
    create_price_fix()
    
    # 总结
    print(f"\n{'='*60}")
    print("诊断总结")
    print("=" * 60)
    
    if issues:
        print("发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"{i}. {issue}")
    else:
        print("✓ 未发现明显问题")
    
    print("\n建议的解决方案:")
    print("1. 检查交易对名称是否正确")
    print("2. 确认网络连接正常")
    print("3. 使用生成的修复代码")
    print("4. 检查API密钥权限")
    print("5. 尝试不同的交易所")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    main()
